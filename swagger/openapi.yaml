openapi: 3.0.1
info:
  title: Concierge API
  description: API Documentation
  version: "1.0"
servers:
  - url: http://localhost:8080
    description: Generated server url
tags:
  - name: sales-manager-controller
    description: API для взаимодействия с заявками на покупку
  - name: offers-controller
    description: API для взаимодействия с предложениями
  - name: rejection-reason-controller
    description: API для работы с причинами отклонений
  - name: shipment-controller
    description: API для работы с товарами в заявках
  - name: concierge-personal-shopper-controller
    description: API для управления PersonalShopper
  - name: concierge-comments-controller
    description: API для взаимодействия с комментариями к заявкам на покупку
  - name: manual-purchase-order-assignment-controller
    description: API ручного назначения сотрудников на заявки
  - name: concierge-purchase-order-controller
    description: API для взаимодействия с заявками на покупку
  - name: concierge-state-history-controller
    description: API для получения истории изменения состояний заявок
paths:
  /api/v1/shipments/comment:
    put:
      tags:
        - shipment-controller
      summary: Добавление комментария к товару
      description: Добавляет комментарий к товару в заявке
      operationId: addCommentToShipment
      parameters:
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Комментарий к товару
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShipmentCommentDTO"
        required: true
      responses:
        "200":
          description: Комментарий успешно добавлен
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShipmentResponseDTO"
        "404":
          description: Товар не найден
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShipmentResponseDTO"
  /api/v1/shipments/shipments/purchase-order/{orderId}:
    post:
      tags:
        - shipment-controller
      summary: Добавление нескольких товаров в заявку
      description: Добавляет несколько новых товаров в указанную заявку
      operationId: addShipmentsToOrder
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Запрос с данными для создания товаров в заявке
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/ShipmentRequestDTO"
        required: true
      responses:
        "200":
          description: Товары успешно добавлены
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ShipmentResponseDTO"
        "404":
          description: Заявка не найдена
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ShipmentResponseDTO"
  /api/v1/shipments/purchase-order/{orderId}:
    post:
      tags:
        - shipment-controller
      summary: Добавление товара в заявку
      description: Добавляет новый пустой товар в указанную заявку
      operationId: addShipmentToOrder
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Товар успешно добавлен
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShipmentResponseDTO"
        "404":
          description: Заявка не найдена
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShipmentResponseDTO"
  /api/v1/purchase/{orderId}/transition:
    post:
      tags:
        - concierge-purchase-order-controller
      summary: Изменение статуса заявки
      description: Изменение статуса заявки
      operationId: changeStateOperation
      parameters:
        - name: orderId
          in: path
          description: ID заявки на покупку
          required: true
          schema:
            type: integer
            format: int64
        - name: eventCode
          in: query
          description: Обрабатываемое событие
          required: true
          schema:
            type: string
            enum:
              - SEND_TO_SALES
              - SALES_WORK_START
              - SEND_TO_SOURCER
              - SOURCER_WORK_START
              - SEND_PROPOSAL_TO_SALES
              - SEND_PURPOSAL_TO_CLIENT
              - CLIENT_REPEAT_REQUEST
              - PAYED_BY_CUSTOMER
              - BUYER_FAILED
              - PROCEED_ORDER
              - ORDER_DONE
              - SALES_PURCHASE_CANCELED
              - SOURCER_PURCHASE_CANCELED
              - SEND_TO_REJECTION
              - RESTORE_BY_SALES
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
        - name: role
          in: query
          description: Роль пользователя
          required: false
          schema:
            $ref: "#/components/schemas/Roles"
      responses:
        "200":
          description: Состояние успешно изменено
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
  /api/v1/purchase/{orderId}/returnToWork:
    post:
      tags:
        - concierge-purchase-order-controller
      summary: Возврат заявки в работу
      description: Возврат заявки в работу
      operationId: returnToWork
      parameters:
        - name: userId
          in: query
          description: "ID пользователя, выполняющего запрос"
          required: true
          schema:
            type: integer
            format: int64
          example: 12345
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
        - name: comment
          in: query
          description: Описание причины возврата
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
  /api/v1/purchase/{orderId}/rejection:
    post:
      tags:
        - concierge-purchase-order-controller
      summary: Создание события отклонения
      description: Добавляет событие отклонения к заявке.
      operationId: createRejectionEvent
      parameters:
        - name: userId
          in: query
          description: "Идентификатор пользователя, выполняющего отклонение"
          required: true
          schema:
            type: integer
            format: int64
          example: 789
        - name: orderId
          in: path
          description: Идентификатор заявки
          required: true
          schema:
            type: integer
            format: int64
          example: 789
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RejectionEventRequest"
        required: true
      responses:
        "200":
          description: Заявка успешно получена
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
        "404":
          description: Заявка или причина отклонения не найдена
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
  /api/v1/purchase/{orderId}/comments:
    get:
      tags:
        - concierge-comments-controller
      summary: Список комментариев по заявке
      description: Список комментариев по заявке
      operationId: getPurchaseOrderComments
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Список комментариев
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentListDTO"
        "404":
          description: Не найдена заявка по запросу
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentListDTO"
        "500":
          description: Внутренняя ошибка
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentListDTO"
    post:
      tags:
        - concierge-comments-controller
      summary: Добавление нового комментария
      description: Добавление нового комментария к заявке
      operationId: addNewComment
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Данные нового комментария
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CommentChangeRequest"
        required: true
      responses:
        "200":
          description: Комментарий успешно создан
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
        "404":
          description: Не найдена заявка по запросу
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
        "500":
          description: Внутренняя ошибка
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
  /api/v1/purchase/purchase/with-shipments/create:
    post:
      tags:
        - concierge-purchase-order-controller
      summary: Создание заявки на покупку со списком товаров
      description: Создание заявки на покупку и старт бизнес-процесса
      operationId: createPurchaseOrderWithShipments
      parameters:
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Запрос с данными для создания заявки
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PurchaseOrderWithShipmentsCreateRequest"
        required: true
      responses:
        "200":
          description: Заявка успешно создана
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
  /api/v1/purchase/process-order:
    post:
      tags:
        - concierge-purchase-order-controller
      summary: Обработка информации о заказе
      description: |
        Обрабатывает информацию о заказе:
        - Ищет все заявки в статусе AWAITING_CLIENT_ANSWER для указанного clientId
        - В этих заявках ищет в привязанных offer и proposedOffer указанный productId
        - Если найдено, добавляет заказ в orderId и в список orders в purchaseOrder
        - Возвращает пары [bitrixId, orderId] для предложений с isSentToCustomer = true
      operationId: processOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OrderInfoDTO"
        required: true
      responses:
        "200":
          description: Успешный запрос
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/OfferPairDTO"
        "204":
          description: Соответствие не найдено
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/OfferPairDTO"
        "400":
          description: Неверные параметры запроса
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/OfferPairDTO"
        "500":
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/OfferPairDTO"
  /api/v1/purchase/orders:
    post:
      tags:
        - concierge-purchase-order-controller
      summary: Получение списка заявок по фильтру
      description: |
        Возвращает список заявок, отфильтрованных по заданным параметрам.
        Для администраторов возвращаются все заявки, для обычных пользователей - только их собственные.
        Поддерживает пагинацию, сортировку и полнотекстовый поиск.
      operationId: getPurchaseOrders
      parameters:
        - name: userId
          in: query
          description: "ID пользователя, выполняющего запрос"
          required: true
          schema:
            type: integer
            format: int64
          example: 12345
        - name: role
          in: query
          description: Роль пользователя
          required: true
          schema:
            $ref: "#/components/schemas/Roles"
        - name: searchText
          in: query
          description: Текст для полнотекстового поиска по заявкам
          required: false
          schema:
            type: string
          example: срочный заказ
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RequestsFilter"
        required: true
      responses:
        "200":
          description: Успешный запрос. Возвращает список DTO заявок
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedResult"
        "400":
          description: Неверные параметры запроса (невалидный фильтр)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedResult"
        "500":
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedResult"
  /api/v1/purchase/order/{orderId}/assign/sourcer:
    post:
      tags:
        - manual-purchase-order-assignment-controller
      summary: Назначить Сорсера на заявку
      description: Ручная инициализация процесса назначения Сорсера на заявку
      operationId: assignOrderToSourcer
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Процесс успешно запущен
  /api/v1/purchase/order/{orderId}/assign/sales:
    post:
      tags:
        - manual-purchase-order-assignment-controller
      summary: Назначить Сейлза на заявку
      description: Ручная инициализация процесса назначения Сейлза на заявку
      operationId: assignOrderToSales
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Процесс успешно запущен
  /api/v1/purchase/order/{orderId}/assign/admin/sourcer:
    post:
      tags:
        - manual-purchase-order-assignment-controller
      summary: Назначить Админа на заявку Сорсера
      description: Назначение Админа Сорсеров на заявку Сорсера
      operationId: assignOrderToAdminSourcer
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Данные Админа для назначения заявки
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SourcerInfoDTO"
        required: true
      responses:
        "200":
          description: Админ назначен на заявку
  /api/v1/purchase/order/{orderId}/assign/admin/sales:
    post:
      tags:
        - manual-purchase-order-assignment-controller
      summary: Назначить Админа на заявку Сейлза
      description: Назначение Админа (сейлза или бутиков) на заявку Сейлза
      operationId: assignOrderToAdminSales
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Данные Админа для назначения заявки
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SalesInfoDTO"
        required: true
      responses:
        "200":
          description: Админ назначен на заявку
  /api/v1/purchase/filter:
    post:
      tags:
        - concierge-purchase-order-controller
      summary: Получение данных для фильтрации заявок
      description: Формирование фильтра для получения заявок
      operationId: getFilterPurchaseOrders
      parameters:
        - name: userId
          in: query
          description: Идентификатор пользователя
          required: true
          schema:
            type: integer
            format: int64
          example: 789
        - name: role
          in: query
          description: Роль пользователя
          required: true
          schema:
            $ref: "#/components/schemas/Roles"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PurchaseOrderFilter"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFilter"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFilter"
  /api/v1/purchase/create:
    post:
      tags:
        - concierge-purchase-order-controller
      summary: Создание заявки на покупку
      description: Создание заявки на покупку и старт бизнес-процесса
      operationId: createPurchaseOrder
      parameters:
        - name: userId
          in: query
          description: ID пользователя
          required: false
          schema:
            type: integer
            format: int64
      requestBody:
        description: Запрос с данными для создания заявки
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PurchaseOrderCreateRequest"
        required: true
      responses:
        "200":
          description: Заявка успешно создана
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
  /api/v1/personal-shopper/shoppers:
    post:
      tags:
        - concierge-personal-shopper-controller
      summary: Получение списка PersonalShopper по фильтру
      description: "Возвращает список PersonalShopper, отфильтрованных по заданным\
        \ параметрам."
      operationId: getPersonalShoppers
      parameters:
        - name: userId
          in: query
          description: "ID пользователя, выполняющего запрос"
          required: true
          schema:
            type: integer
            format: int64
          example: 12345
        - name: searchText
          in: query
          description: Текст для полнотекстового поиска по заявкам
          required: false
          schema:
            type: string
          example: срочный заказ
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PersonalShopperFilter"
        required: true
      responses:
        "200":
          description: Успешный запрос. Возвращает список DTO PersonalShopper
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedShoppersResult"
        "400":
          description: Неверные параметры запроса (невалидный фильтр)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedShoppersResult"
        "500":
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedShoppersResult"
  /api/v1/personal-shopper/create:
    post:
      tags:
        - concierge-personal-shopper-controller
      summary: Создание нового PersonalShopper
      description: Добавляет нового PersonalShopper в систему на основе предоставленных
        данных
      operationId: createPersonalShopper
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PersonalShopperCreateRequest"
        required: true
      responses:
        "201":
          description: PersonalShopper успешно создан
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PersonalShopper"
  /api/v1/offer/shoppers:
    post:
      tags:
        - offers-controller
      summary: Добавление шоперов в предложение
      description: Добавление шоперов в предложение
      operationId: addingShoppers
      parameters:
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Данные шоперов
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShipmentOffersDTO"
        required: true
      responses:
        "200":
          description: Шопер(ы) успешно добавлен в предложение.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShipmentOffersDTO"
  /api/v1/offer/shopperOffer:
    post:
      tags:
        - offers-controller
      summary: Добавить предложение шопера
      description: Добавить предложение шопера
      operationId: addShopperOffer
      parameters:
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Данные по предложению шоперов
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShipmentOffersDTO"
        required: true
      responses:
        "200":
          description: Предложение успешно добавлено
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShipmentOffersDTO"
  /api/v1/offer/send:
    post:
      tags:
        - offers-controller
      summary: Отправка предложений клиенту в WhatsApp
      description: Отправить несколько предложений клиенту в WhatsApp
      operationId: sendOffers
      parameters:
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Данные по предложениям для отправки сообщения
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SendOffersToClientRequest"
        required: true
      responses:
        "200":
          description: Предложение успешно добавлено
          content:
            application/json: {}
  /api/v1/offer/products:
    post:
      tags:
        - offers-controller
      summary: Добавление товара с платформы в предложение
      description: Добавление товара с платформы в предложение
      operationId: addingProducts
      parameters:
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Список товаров с платформы
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShipmentOffersDTO"
        required: true
      responses:
        "200":
          description: Товары успешно добавлены в предложение.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShipmentOffersDTO"
  /api/v1/shipments/{shipmentId}:
    delete:
      tags:
        - shipment-controller
      summary: Удаление товара из заявки
      description: Удаляет товар из заявки. Доступно только для заявок в статусе 'В
        работе у Sales'
      operationId: deleteShipment
      parameters:
        - name: shipmentId
          in: path
          description: ID товара
          required: true
          schema:
            type: integer
            format: int64
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "204":
          description: Товар успешно удален
        "400":
          description: Невозможно удалить товар (неверный статус заявки)
        "404":
          description: Товар или заявка не найдены
    patch:
      tags:
        - shipment-controller
      summary: Обновление информации о товаре
      description: Частично обновляет информацию о товаре (PATCH запрос)
      operationId: updateShipment
      parameters:
        - name: shipmentId
          in: path
          description: ID товара
          required: true
          schema:
            type: integer
            format: int64
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Данные для обновления товара
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ShipmentRequestDTO"
        required: true
      responses:
        "200":
          description: Информация о товаре успешно обновлена
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShipmentResponseDTO"
        "404":
          description: Товар не найден
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShipmentResponseDTO"
  /api/v1/purchase/{orderId}:
    get:
      tags:
        - concierge-purchase-order-controller
      summary: Получение заявки на покупку по ID
      description: Получение заявки на покупку по ID
      operationId: getPurchaseOrder
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
        - name: role
          in: query
          description: Роль пользователя
          required: false
          schema:
            $ref: "#/components/schemas/Roles"
        - name: customerId
          in: query
          description: ID пользователя
          required: false
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Заявка успешно получена
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
        "404":
          description: Заявка не найдена
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
    patch:
      tags:
        - concierge-purchase-order-controller
      operationId: updatePurchaseOrder
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Запрос с данными для обновления заявки
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PurchaseOrderUpdateRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseOrderFullDTO"
  /api/v1/purchase/{orderId}/comments/{id}:
    get:
      tags:
        - concierge-comments-controller
      summary: Получение комментария
      description: Получение комментария к заявке
      operationId: getComment
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
        - name: id
          in: path
          description: ID Комментария
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Комментарий получен
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
        "404":
          description: Не найден комментарий
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
        "500":
          description: Внутренняя ошибка
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
    delete:
      tags:
        - concierge-comments-controller
      summary: Удаление комментария
      description: Удаление комментария к заявке
      operationId: deleteComment
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
        - name: id
          in: path
          description: ID Комментария
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Комментарий удален
    patch:
      tags:
        - concierge-comments-controller
      summary: Изменение комментария
      description: Изменение комментария к заявке
      operationId: editComment
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
        - name: id
          in: path
          description: ID Комментария
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        description: Данные измененного комментария
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CommentChangeRequest"
        required: true
      responses:
        "200":
          description: Комментарий успешно изменен
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
        "404":
          description: Не найдена заявка или комментарий по запросу
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
        "500":
          description: Внутренняя ошибка
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
  /api/v1/purchase/{orderId}/comments/{id}/pin:
    patch:
      tags:
        - concierge-comments-controller
      summary: Пин выбранного комментария
      description: Пин комментария к заявке
      operationId: pinComment
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
        - name: id
          in: path
          description: ID Комментария
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Комментарий запинен
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
        "404":
          description: Не найдена заявка или комментарий по запросу
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
        "500":
          description: Внутренняя ошибка
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommentFullDTO"
  /api/v1/sales-manager/sales-orders:
    get:
      tags:
        - sales-manager-controller
      summary: Получить список товаров на покупку
      description: Получить список товаров на покупку
      operationId: getSalesOrders
      parameters:
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
        - name: shipmentId
          in: query
          description: ID товара для заявки
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Список товаров на покупку
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/SalesManagerProductDto"
        "400":
          description: Некорректные параметры запроса
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/SalesManagerProductDto"
  /api/v1/rejection-reasons:
    get:
      tags:
        - rejection-reason-controller
      summary: Получение списка причин отклонения
      description: Возвращает список причин отклонения в зависимости от типа объекта
        и статуса заявки (если применимо).
      operationId: getRejectionReasons
      parameters:
        - name: userId
          in: query
          description: "ID пользователя, выполняющего запрос"
          required: true
          schema:
            type: integer
            format: int64
          example: 12345
        - name: objectType
          in: query
          description: Тип объекта (заявка или товар)
          required: true
          schema:
            type: string
            enum:
              - PURCHASE_ORDER
              - PRODUCT
        - name: status
          in: query
          description: Статус заявки (только для типа объекта ORDER)
          required: false
          schema:
            type: string
            description: Статусы заявки на покупку
            enum:
              - CREATED
              - DRAFT
              - NEW
              - IN_PROGRESS_SALES
              - AWAITING_SOURCER
              - IN_PROGRESS_SOURCER
              - AWAITING_SEND_TO_CLIENT
              - AWAITING_CLIENT_ANSWER
              - REPEAT_REQUEST_TO_SALES
              - REPEAT_AWAITING_SOURCER
              - PAYED_ORDER_IN_PROGRESS
              - PAYED_REPEAT_REQUEST
              - PAYED_RR_SOURCER
              - REJECTED
              - DONE
              - CANCELLED
              - ALL
      responses:
        "200":
          description: Список причин отклонения успешно получен
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RejectionReason"
        "400":
          description: "Некорректный запрос: статус указан для неверного типа объе\
            кта"
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RejectionReason"
  /api/v1/purchase/sources:
    get:
      tags:
        - concierge-purchase-order-controller
      summary: Получение списка источников заявок
      description: Получение списка источников заявок
      operationId: getSources
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OrderSources"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OrderSources"
  /api/v1/purchase-order/{orderId}/history:
    get:
      tags:
        - concierge-state-history-controller
      summary: Получение истории изменения состояний
      description: Получение истории изменения состояний заявки с указанием автора
        и времени изменения
      operationId: getStateTransitionHistory
      parameters:
        - name: orderId
          in: path
          description: ID заявки
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: История изменения состояний получена
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PurchaseOrderStateHistoryDTO"
        "404":
          description: Не найдена заявка по указанному ID
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PurchaseOrderStateHistoryDTO"
        "500":
          description: Внутренняя ошибка
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PurchaseOrderStateHistoryDTO"
  /api/v1/personal-shopper/filter:
    get:
      tags:
        - concierge-personal-shopper-controller
      summary: Фильтр для списка PersonalShopper
      description: Возвращает фильтр для PersonalShopper
      operationId: filterPersonalShopper
      responses:
        "200":
          description: Фильтр PersonalShopper успешно получен
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PersonalShopperFilter"
  /api/v1/offer/proposedOffers:
    get:
      tags:
        - offers-controller
      summary: Получение ProposedOffer по идентификатору Offer
      description: Возвращает список ProposedOffer со вложенными сущностями по идентификатору
        Offer.
      operationId: getProposedOffersByOffer
      parameters:
        - name: offerId
          in: query
          description: ID оффера для поиска предложений
          required: true
          schema:
            type: integer
            format: int64
          example: 12345
      responses:
        "200":
          description: Успешный запрос
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ProposedOfferDTO"
        "400":
          description: Неверные параметры запроса
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ProposedOfferDTO"
                uniqueItems: true
  /api/v1/offer/{offerId}:
    delete:
      tags:
        - offers-controller
      summary: Удаление товара из предложения
      description: Удаление товара из предложения
      operationId: deleteProduct
      parameters:
        - name: userId
          in: query
          description: ID пользователя
          required: true
          schema:
            type: integer
            format: int64
        - name: offerId
          in: path
          description: ID офера
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Товар успешно удален из предложения.
components:
  schemas:
    ImageDTO:
      description: "Информация об изображении, прикрепленном к заявке"
      properties:
        id:
          type: string
          format: uuid
          description: Уникальный идентификатор изображения
          example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        url:
          type: string
          description: URL для доступа к изображению
          example: https://example.com/images/photo123.jpg
        creationDate:
          type: string
          format: date-time
          description: Дата и время загрузки изображения
          example: 2023-07-15T10:30:45Z
    DescriptionStructureEnum:
      description: Структура описания перечисляемого значения с локализацией
      properties:
        code:
          type: string
          description: Код значения (техническое обозначение)
        localizedDescription:
          type: string
          description: Локализованное описание значения
        localizedStatusInfo:
          type: string
          description: Локализованная доп информация о статусе
    BuyerOffers:
      type: object
      description: Предложения баера
      properties:
        proposedOffers:
          type: array
          items:
            $ref: "#/components/schemas/ProposedOfferDTO"
          uniqueItems: true
    CurrencyDTO:
      description: DTO для представления валюты
      properties:
        id:
          type: integer
          format: int64
          description: Уникальный идентификатор валюты
          example: 1
        name:
          type: string
          description: Полное наименование валюты
          example: Российский рубль
        sign:
          type: string
          description: Символ валюты
          example: ₽
        isoCode:
          type: string
          description: Буквенный код валюты по стандарту ISO 4217
          example: RUB
        isoNumber:
          type: integer
          format: int32
          description: Числовой код валюты по стандарту ISO 4217
          example: 643
        isBase:
          type: boolean
          description: "Признак того, что валюта является базовой для системы (нап\
            ример, для конвертации)"
          example: true
        isActive:
          type: boolean
          description: "Признак того, что валюта доступна для использования в сист\
            еме"
          example: true
        selectedByDefault:
          type: boolean
          description: "Признак того, что валюта выбрана по умолчанию для отображе\
            ния пользователю"
          example: true
    OfferDTO:
      type: object
      description: Информация о предложении продавца
      properties:
        id:
          type: integer
          format: int64
          description: Уникальный идентификатор предложения
          example: 1
        comparisonCriteria:
          type: array
          items:
            type: string
            enum:
              - PRICE
              - DELIVERY_DATE
          uniqueItems: true
        shipmentId:
          type: integer
          format: int64
          description: Идентификатор заявки
          example: 100
        orderId:
          type: integer
          format: int64
          description: Идентификатор заказа
          example: 200
        seller:
          $ref: "#/components/schemas/SellerInfoDTO"
          description: DTO с информацией о продавца(шопер)
        shopper:
          $ref: "#/components/schemas/ShopperInfoDTO"
          description: DTO с информацией о шопере
        product:
          $ref: "#/components/schemas/ProductPlatformDTO"
          description: Продукт с платформы
        type:
          $ref: "#/components/schemas/OfferType"
          description: Тип предложения
        sellerType:
          type: string
          description: Тип продавца
          example: INDIVIDUAL
        creationDate:
          type: string
          format: date-time
          description: Дата создания предложения
          example: 2023-11-25T15:30:00+03:00
        buyerOffers:
          $ref: "#/components/schemas/BuyerOffers"
          description: Предложения баера
    OfferType:
      type: string
      description: Перечисление типов предложений товаров/услуг
      enum:
        - PLATFORM_PRODUCT
        - BUYER_OFFER
    ProductConditionDTO:
      type: object
      description: Описание состояния товара
      properties:
        id:
          type: integer
          format: int64
          description: ID состояния товара в монолите
          example: 1
        name:
          type: string
          description: Название состояния товара
          example: NEW
        description:
          type: string
          description: Описание состояния товара
          example: Новое с биркой
      required:
        - id
    ProductPlatformDTO:
      type: object
      description: Продукт с платформы
      properties:
        productId:
          type: integer
          format: int64
          description: "Идентификатор товара, существующего на платформе"
          example: 1
        currencyPrice:
          type: number
          description: Цена в валюте
          example: 200.0
        productPhoto:
          $ref: "#/components/schemas/ImageDTO"
          description: Фото товара
        productLocation:
          type: string
          description: Расположение товара
          example: "Москва, склад №1"
        brand:
          type: string
          description: Бренд
          example: Nike
        productCategory:
          type: string
          description: Категория товара
          example: Обувь
        availableSizes:
          type: array
          description: Размеры в наличии
          example:
            - "40"
            - "41"
            - "42"
          items:
            type: string
        priceWithoutDiscount:
          type: number
          description: Цена без скидки
          example: 250.0
        priceWithDiscount:
          type: number
          description: Цена со скидкой (при наличии)
          example: 200.0
        discountAmount:
          type: number
          description: Размер скидки (при наличии)
          example: 50.0
        sizeType:
          type: string
          description: Тип размера
          example: INT
        conditionId:
          type: integer
          format: int32
          description: Идентификатор состояния товара
          example: 1
        conditionName:
          type: string
          description: Название состояния товара
          example: Новое с биркой
        productState:
          type: string
          description: Состояние публикации товара
          example: PUBLISHED
        likesCount:
          type: integer
          format: int32
          description: Количество лайков
          example: 0
        isLiked:
          type: boolean
          description: "Флаг, указывающий, лайкнут ли товар пользователем"
          example: false
        url:
          type: string
          description: URL товара
          example: /products/shorty-fear-of-god-essentials-new-3013660
        discount:
          type: number
          description: Размер скидки в процентах
        currency:
          $ref: "#/components/schemas/CurrencyDTO"
          description: Информация о валюте
        isSold:
          type: boolean
          description: "Флаг, указывающий, что продукт продан"
          example: false
    ProposedOfferDTO:
      type: object
      description: Данные о предложении продавца
      properties:
        id:
          type: integer
          format: int64
          description: Уникальный идентификатор предложения
          example: 123
        proposedProduct:
          $ref: "#/components/schemas/ProductPlatformDTO"
          description: Товар созданный по предложению шоппера
          example: 12345
        rublePrice:
          type: number
          description: Цена предложения в рублях
          example: 15000.0
        deliveryDate:
          type: string
          format: date-time
          description: Дата доставки по предложению
          example: 2023-12-31T15:00:00+03:00
        validUntil:
          type: string
          format: date-time
          description: Срок действия предложения
          example: 2023-12-15T23:59:59+03:00
        creationDate:
          type: string
          format: date-time
          description: Дата создания предложения
          example: 2023-11-30T12:00:00+03:00
        currency:
          type: string
          description: Валюта закупки
          enum:
            - USD
            - AED
            - EUR
            - KRW
            - KGS
            - RUB
            - TL
            - CNY
            - JPY
          example: EUR
        currencyPrice:
          type: number
          description: Цена в валюте закупки
          example: 150.0
        currencyRate:
          type: number
          description: Курс валюты к рублю
          example: 90.45
        hasReceipt:
          type: boolean
          description: Флаг наличия чека
          example: true
        isCompleteSet:
          type: boolean
          description: Флаг полного комплекта товара
          example: true
        hasCustomCommission:
          type: boolean
          description: Флаг произвольной комиссии
          example: false
        commission:
          type: number
          description: Размер комиссии в процентах
          example: 15.0
        comment:
          type: string
          description: Комментарий к предложению
          example: "Товар в отличном состоянии, найден в бутике в Милане"
        orderId:
          type: integer
          format: int64
          description: Идентификатор заказа
          example: 200
        productCondition:
          $ref: "#/components/schemas/ProductConditionDTO"
          description: Информация о состоянии товара
          example: 1
        offerId:
          type: integer
          format: int64
          description: "Идентификатор оффера, к которому относится предложение"
          example: 456
      required:
        - commission
        - currency
        - currencyPrice
        - currencyRate
        - deliveryDate
        - hasCustomCommission
        - hasReceipt
        - isCompleteSet
        - productCondition
        - validUntil
    SellerInfoDTO:
      description: Информация о продавце/шопера товара
      properties:
        sellerId:
          type: integer
          format: int64
          description: Уникальный идентификатор продавца/шопера в системе
          example: 12345
        sellerType:
          $ref: "#/components/schemas/DescriptionStructureEnum"
        sellerFio:
          type: string
          description: ФИО продавца/шопера
          example: Иванов Иван Иванович
          maxLength: 100
        sellerEmail:
          type: string
          format: email
          description: Электронная почта продавца/шопера
          example: <EMAIL>
          maxLength: 50
        sellerNickname:
          type: string
          description: Никнейм продавца/шопера в системе
          example: best_seller_2023
          maxLength: 30
        urlAvatar:
          type: string
          description: Ссылка на аватар продавца/шопера
          example: https://example.com/avatar.jpg
          maxLength: 200
      required:
        - sellerId
    ShimpentSizeDTO:
      description: DTO для представления размера
      example: M
      properties:
        type:
          type: string
          description: Тип размера
          example: Large
        sizeId:
          type: integer
          format: int64
          description: Идентификатор размера
          example: 123
        availableSizes:
          type: array
          description: Доступные размеры
          items:
            type: string
            description: Доступные размеры
          uniqueItems: true
    ShipmentResponseDTO:
      type: object
      description: DTO с информацией о товаре в заявке
      properties:
        id:
          type: integer
          format: int64
          description: ID товара
          example: 1
        purchaseOrderId:
          type: integer
          format: int64
          description: "ID заявки, к которой относится товар"
          example: 100
        categoryId:
          type: integer
          format: int64
          description: ID категории товара
          example: 123
        categoryName:
          type: string
          description: Наименование категории товара
          example: Джемперы и свитеры
        brandId:
          type: integer
          format: int64
          description: ID бренда товара
          example: 456
        brandName:
          type: string
          description: Наименование бренда товара
          example: ЦВЦ СТОУНЗ
        materialAttributeId:
          type: integer
          format: int64
          description: ID атрибута материала товара
          example: 789
        materialAttributeName:
          type: string
          description: Наименование атрибута материала товара
          example: Синтетика
        colorAttributeId:
          type: integer
          format: int64
          description: ID атрибута цвета товара
          example: 101
        colorAttributeName:
          type: string
          description: Наименование атрибута цвета товара
          example: Оранжевый
        createdAt:
          type: string
          description: Дата создания записи о товаре
          example: 2023-01-01T12:00:00Z
        modelId:
          type: integer
          format: int64
          description: ID модели товара
          example: 202
        modelName:
          type: string
          description: Наименование модели товара
          example: Twist
        shipmentSize:
          $ref: "#/components/schemas/ShimpentSizeDTO"
          description: Размеры товара
        description:
          type: string
          description: Описание товара
          example: Кожаная куртка черного цвета
        images:
          type: array
          description: Список изображений товара
          items:
            $ref: "#/components/schemas/ImageDTO"
        links:
          type: array
          description: Ссылки на товар
          example:
            - https://example.com/product1
          items:
            type: string
        comment:
          type: string
          description: Комментарий к товару
          example: "Маленькая, бежевая"
        offers:
          type: array
          description: Список предложений продавцов
          items:
            $ref: "#/components/schemas/OfferDTO"
        orders:
          type: array
          description: "Список ID заказов, для все подчиненных заказов"
          items:
            type: integer
            format: int64
    ShopperInfoDTO:
      description: Информация о шопере
      properties:
        id:
          type: integer
          format: int64
          description: Идентификатор шопера
        userId:
          type: integer
          format: int64
          description: Идентификатор пользователя (шопера)
        nickName:
          type: string
          description: Никнейм шопера
        fio:
          type: string
          description: ФИО шопера
        urlAvatar:
          type: string
          description: URL аватара
        paymentFormat:
          $ref: "#/components/schemas/DescriptionStructureEnum"
        interactionTypes:
          type: array
          description: Типы взаимодействия
          items:
            $ref: "#/components/schemas/DescriptionStructureEnum"
          uniqueItems: true
    ShipmentCommentDTO:
      type: object
      description: DTO для добавления комментария к товару в заявке
      properties:
        shipmentId:
          type: integer
          format: int64
          description: ID товара
          example: 123
        comment:
          type: string
          description: Комментарий к товару
          example: "Маленькая, бежевая"
    ShipmentRequestDTO:
      type: object
      description: DTO для создания или обновления товара в заявке
      properties:
        categoryId:
          type: integer
          format: int64
          description: ID категории товара
          example: 123
        categoryName:
          type: string
          description: Наименование категории товара
        creatorId:
          type: integer
          format: int64
          description: ID создателя товара
          example: 123
        brandId:
          type: integer
          format: int64
          description: ID бренда товара
          example: 456
        brandName:
          type: string
          description: Наименование бренда товара
        brandTransliterateName:
          type: string
          description: Наименование бренда товара (транслитерация)
        materialAttributeId:
          type: integer
          format: int64
          description: ID атрибута материала товара
          example: 789
        materialAttributeName:
          type: string
          description: Наименование атрибута материала товара
          example: Синтетика
        colorAttributeId:
          type: integer
          format: int64
          description: ID атрибута цвета товара
          example: 101
        colorAttributeName:
          type: string
          description: Наименование цвета товара
        modelId:
          type: integer
          format: int64
          description: ID модели товара
          example: 202
        modelName:
          type: string
          description: Наименование модели товара
        shipmentSize:
          $ref: "#/components/schemas/ShimpentSizeDTO"
          description: Размеры товара
        description:
          type: string
          description: Описание товара
          example: Кожаная куртка черного цвета
        images:
          type: array
          description: Список изображений товара
          items:
            $ref: "#/components/schemas/ImageDTO"
        links:
          type: array
          description: Ссылки на товар
          example:
            - https://example.com/product1
          items:
            type: string
        comment:
          type: string
          description: Комментарий к товару
          example: "Маленькая, бежевая"
    CommentFullDTO:
      description: Полная информация о комментарии к заказу
      properties:
        id:
          type: integer
          format: int64
          description: Уникальный идентификатор комментария
          example: 12345
        authorId:
          type: integer
          format: int64
          description: ID автора комментария
          example: 67890
        isPined:
          type: boolean
          description: "Признак закрепления комментария (true - закреплен, false -\
            \ не закреплен)"
          example: false
        message:
          type: string
          description: Текст сообщения комментария
          example: Необходимо уточнить параметры заказа у клиента
        datetime:
          type: string
          format: date-time
          description: Дата и время создания комментария
          example: 2023-07-15T14:30:00Z
    CustomerInfoDTO:
      description: DTO с основной информацией о клиенте
      properties:
        customerId:
          type: integer
          format: int64
          description: Уникальный идентификатор клиента
          example: 12345
        customerNickName:
          type: string
          description: Полное имя клиента
          example: Иванов Иван Иванович
          maxLength: 255
        lastname:
          type: string
          description: Имя клиента
          example: Иван
          maxLength: 100
        firstname:
          type: string
          description: Фамилия клиента
          example: Иванов
          maxLength: 100
        customerPhone:
          type: string
          description: Контактный телефон клиента
          example: "+79161234567"
          pattern: "^\\+[0-9]{1,15}$"
        customerEmail:
          type: string
          format: email
          description: Электронная почта клиента
          example: <EMAIL>
          maxLength: 320
        urlAvatar:
          type: string
          description: Ссылка на картинку аватара
          example: https://example.com/avatar.jpg
        hasActiveBans:
          type: boolean
          description: Признак наличия активных блокировок у клиента
          example: false
    PurchaseOrderFullDTO:
      type: object
      description: "Полные данные о заказе на покупку, включая всю связанную инфор\
        мацию"
      properties:
        id:
          type: integer
          format: int64
          description: Уникальный идентификатор заказа
          example: 12345
        customer:
          $ref: "#/components/schemas/CustomerInfoDTO"
          description: "ID клиента, оформившего заказ"
          example: 67890
        source:
          type: string
          description: "Источник, откуда поступил заказ"
          enum:
            - TELEGRAM
            - WHATSAPP
            - INSTAGRAM
            - COMMENTS
            - SHOWCASE
            - CONCIERGE
            - CANCELLED_ORDERS_NOT_CONFIRMED
            - CANCELLED_ORDERS_NOT_SHIPPED
            - CANCELLED_ORDERS_NOT_APPROVED
            - PERSONAL_SHOPPING
            - PRODUCT_PAGE
            - WISHLIST
            - CART
            - O_TRENDS
            - SALES_ADMIN
            - SALES_APP
            - CS_SUPPORT_CHAT
            - PARTNERS_AFFILIATE
        description:
          type: string
          description: Описание заказа
          example: Канцтовары для офиса на 3 квартал
        creationDate:
          type: string
          format: date-time
          description: Дата и время создания заказа
          example: 2023-07-15T10:30:45Z
        changeDate:
          type: string
          format: date-time
          description: Дата и время последнего изменения заказа
          example: 2023-07-16T14:25:30Z
        status:
          $ref: "#/components/schemas/DescriptionStructureEnum"
          description: Текущий статус заказа
        sourcerInfo:
          $ref: "#/components/schemas/SourcerInfoDTO"
          description: Информация по менеджеру подбора предложения заказа
        salesInfo:
          $ref: "#/components/schemas/SalesInfoDTO"
          description: Информация по менеджеру по продажам
        images:
          type: array
          description: "Список изображений, связанных с заказом"
          items:
            $ref: "#/components/schemas/ImageDTO"
        orders:
          type: array
          description: "Список ID заказов, включенных в этот заказ на покупку"
          items:
            type: integer
            format: int64
        comments:
          type: array
          description: Список комментариев к этому заказу
          items:
            $ref: "#/components/schemas/CommentFullDTO"
        rejectionReason:
          type: string
          description: "Причина отказа в заказе, если применимо"
          example: Нет в наличии
        rejectionDescription:
          type: string
          description: "Подробное описание причины отказа, если применимо"
          example: Запрашиваемые товары в настоящее время отсутствуют на складе и
            будут доступны только в следующем месяце
        shipments:
          type: array
          description: "Список товаров, включенных в этот заказ на покупку"
          items:
            $ref: "#/components/schemas/ShipmentResponseDTO"
        link:
          type: string
          description: Ссылка на товар
          example: www.jacquemus.com
    Roles:
      type: string
      description: Перечисление ролей пользователей в системе
      enum:
        - SALES
        - SOURCER
        - CUSTOMER
        - STOLESHNIKOV_BOUTIQUE_SALESMAN
        - KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN
        - STOLESHNIKOV_ADMIN
        - KUZNETSKY_BRIDGE_ADMIN
        - CONCIERGE_SALES_ADMIN
        - CONCIERGE_SOURCERS_ADMIN
        - MERCAUX_ADMIN
        - SYSTEM
      example: SALES
    SalesInfoDTO:
      description: DTO с информацией о продавце/менеджере по продажам
      properties:
        salesId:
          type: integer
          format: int64
          description: Уникальный идентификатор продавца в системе
          example: 789
        fio:
          type: string
          description: "Фамилия, имя и отчество продавца"
          example: Петрова Анна Сергеевна
          maxLength: 255
        nickName:
          type: string
          description: Никнейм продавца
          example: petrova
          maxLength: 255
        urlAvatar:
          type: string
          description: Ссылка на картинку аватара
          example: https://example.com/avatar.jpg
        salesRole:
          $ref: "#/components/schemas/Roles"
        email:
          type: string
          description: Почта продавца
          example: <EMAIL>
    SourcerInfoDTO:
      description: |-
        Информация о менеджере подбора предложения заказа.
        Содержит идентификационные данные менеджера.
      properties:
        sourcerId:
          type: integer
          format: int64
          description: Внутренний уникальный идентификатор менеджера в БД
          example: 789012
          minimum: 1
        nickName:
          type: string
          description: Уникальное имя менеджера в системе
          example: best_sourcer_42
        fio:
          type: string
          description: "Фамилия, имя и отчество менеджера"
          example: Петрова Анна Сергеевна
          maxLength: 255
        urlAvatar:
          type: string
          description: Ссылка на картинку аватара
          example: https://example.com/avatar.jpg
        email:
          type: string
          description: Почта менеджера для поиска в Bitrix
          example: <EMAIL>
    RejectionEventRequest:
      type: object
      description: Данные для создания события отклонения
      properties:
        objectId:
          type: integer
          format: int64
          description: Идентификатор отклоняемого объекта
          example: 12345
        rejectionReason:
          type: string
          description: Причина отклонения
          example: Не соответствует требованиям
        additionalText:
          type: string
          description: Дополнительное описание
          example: Отсутствуют необходимые документы
      required:
        - objectId
        - rejectionReason
    CommentChangeRequest:
      type: object
      properties:
        authorId:
          type: integer
          format: int64
        message:
          type: string
          maxLength: 2000
          minLength: 0
    PurchaseOrderWithShipmentsCreateRequest:
      type: object
      description: DTO для создания новой заявки на покупку со списком товаров
      properties:
        customerInfo:
          $ref: "#/components/schemas/CustomerInfoDTO"
          description: Информация о покупателе
        sourcerInfo:
          $ref: "#/components/schemas/SourcerInfoDTO"
          description: Информация о менеджере подбора предложения
        salesInfo:
          $ref: "#/components/schemas/SalesInfoDTO"
          description: Информация о менеджере по продажам
        source:
          type: string
          default: WHATSAPP
          description: Источник создания заявки
          enum:
            - TELEGRAM
            - WHATSAPP
            - INSTAGRAM
            - COMMENTS
            - SHOWCASE
            - CONCIERGE
            - CANCELLED_ORDERS_NOT_CONFIRMED
            - CANCELLED_ORDERS_NOT_SHIPPED
            - CANCELLED_ORDERS_NOT_APPROVED
            - PERSONAL_SHOPPING
            - PRODUCT_PAGE
            - WISHLIST
            - CART
            - O_TRENDS
            - SALES_ADMIN
            - SALES_APP
            - CS_SUPPORT_CHAT
            - PARTNERS_AFFILIATE
        description:
          type: string
          description: Описание заявки
          example: Необходимо закупить офисную технику
        imagesUrl:
          type: array
          description: Ссылки на изображения
          items:
            type: string
        initialStatus:
          type: string
          default: AWAITING_SOURCER
          description: Начальный статус заявки
          enum:
            - CREATED
            - DRAFT
            - NEW
            - IN_PROGRESS_SALES
            - AWAITING_SOURCER
            - IN_PROGRESS_SOURCER
            - AWAITING_SEND_TO_CLIENT
            - AWAITING_CLIENT_ANSWER
            - REPEAT_REQUEST_TO_SALES
            - REPEAT_AWAITING_SOURCER
            - PAYED_ORDER_IN_PROGRESS
            - PAYED_REPEAT_REQUEST
            - PAYED_RR_SOURCER
            - REJECTED
            - DONE
            - CANCELLED
            - ALL
          example: AWAITING_SOURCER
        shipments:
          type: array
          description: Товары в заявке
          items:
            $ref: "#/components/schemas/ShipmentRequestDTO"
      required:
        - customerInfo
        - salesInfo
        - source
        - sourcerInfo
    OfferPairDTO:
      type: object
      description: Пара bitrixId и orderId из предложения
      properties:
        bitrixId:
          type: integer
          format: int64
          description: Идентификатор в Bitrix
          example: 123
        orderId:
          type: integer
          format: int64
          description: Идентификатор заказа
          example: 456
    OrderInfoDTO:
      type: object
      description: Информация о заказе
      properties:
        productIds:
          type: array
          description: Идентификатор продукта
          items:
            type: integer
            format: int64
          uniqueItems: true
        orderID:
          type: integer
          format: int64
          description: Идентификатор заказа
          example: 456
        clientId:
          type: integer
          format: int64
          description: Идентификатор клиента
          example: 789
    OrdersForConciergeDTO:
      type: object
      description: DTO для отображения информации о заказах консьержа
      properties:
        id:
          type: integer
          format: int64
          description: Уникальный идентификатор заказа
          example: 12345
        description:
          type: string
          description: Описание заявки
        customerInfo:
          $ref: "#/components/schemas/CustomerInfoDTO"
          description: Информация по кастомеру
        sourcerInfo:
          $ref: "#/components/schemas/SourcerInfoDTO"
          description: Информация по менеджеру подбора предложения заказа
        salesInfo:
          $ref: "#/components/schemas/SalesInfoDTO"
          description: Информация по менеджеру по продажам
        source:
          type: string
          description: Источник создания заказа
          enum:
            - TELEGRAM
            - WHATSAPP
            - INSTAGRAM
            - COMMENTS
            - SHOWCASE
            - CONCIERGE
            - CANCELLED_ORDERS_NOT_CONFIRMED
            - CANCELLED_ORDERS_NOT_SHIPPED
            - CANCELLED_ORDERS_NOT_APPROVED
            - PERSONAL_SHOPPING
            - PRODUCT_PAGE
            - WISHLIST
            - CART
            - O_TRENDS
            - SALES_ADMIN
            - SALES_APP
            - CS_SUPPORT_CHAT
            - PARTNERS_AFFILIATE
        creationDate:
          type: string
          format: date-time
          description: Дата и время создания заказа
          example: 2023-05-15T10:30:45+03:00
        status:
          $ref: "#/components/schemas/DescriptionStructureEnum"
          description: Текущий статус заказа
        orders:
          type: array
          description: Список идентификаторов связанных заказов
          example:
            - 101
            - 102
            - 201
          items:
            type: integer
            format: int64
        shipments:
          type: array
          description: "Список товаров, включенных в этот заказ на покупку"
          items:
            $ref: "#/components/schemas/ShipmentResponseDTO"
        link:
          type: string
          description: Ссылка на товар
          example: www.jacquemus.com
        images:
          type: array
          description: "Список изображений, связанных с заказом"
          items:
            $ref: "#/components/schemas/ImageDTO"
      required:
        - id
    PaginatedResult:
      type: object
      description: Результат запроса с пагинацией
      properties:
        data:
          type: array
          description: Список элементов данных для текущей страницы
          items:
            $ref: "#/components/schemas/OrdersForConciergeDTO"
        totalPages:
          type: integer
          format: int64
          description: "Общее количество страниц всех элементов, соответствующих к\
            ритериям фильтрации"
          example: 10
        totalCount:
          type: integer
          format: int64
          description: "Общее количество элементов, доступных по всем страницам (с\
            оответствующих критериям фильтрации)"
          example: 153
      required:
        - totalCount
        - totalPages
    SortingOption:
      type: object
      description: Single sorting option
      properties:
        code:
          $ref: "#/components/schemas/TypesSorting"
          description: Sorting code (enum value)
        description:
          type: string
          description: Human-readable sorting name
          example: Сначала новые
        isSelected:
          type: boolean
          description: Flag indicating if this option is currently selected
          example: true
      required:
        - code
        - description
        - isSelected
    TypesSorting:
      type: string
      default: DATE
      description: Определяет доступные типы сортировки для наборов результатов
      enum:
        - STATUS
        - DATE
    RequestsFilter:
      type: object
      description: Фильтр для получения заявок
      properties:
        orderStatusEnums:
          type: array
          description: Список группировок статусов с количеством
          items:
            type: string
            description: Статусы заявки на покупку
            enum:
              - CREATED
              - DRAFT
              - NEW
              - IN_PROGRESS_SALES
              - AWAITING_SOURCER
              - IN_PROGRESS_SOURCER
              - AWAITING_SEND_TO_CLIENT
              - AWAITING_CLIENT_ANSWER
              - REPEAT_REQUEST_TO_SALES
              - REPEAT_AWAITING_SOURCER
              - PAYED_ORDER_IN_PROGRESS
              - PAYED_REPEAT_REQUEST
              - PAYED_RR_SOURCER
              - REJECTED
              - DONE
              - CANCELLED
              - ALL
        orderSourceEnums:
          type: array
          description: Список группировок источников с количеством
          items:
            type: string
            description: Источники создания заявки на покупку
            enum:
              - TELEGRAM
              - WHATSAPP
              - INSTAGRAM
              - COMMENTS
              - SHOWCASE
              - CONCIERGE
              - CANCELLED_ORDERS_NOT_CONFIRMED
              - CANCELLED_ORDERS_NOT_SHIPPED
              - CANCELLED_ORDERS_NOT_APPROVED
              - PERSONAL_SHOPPING
              - PRODUCT_PAGE
              - WISHLIST
              - CART
              - O_TRENDS
              - SALES_ADMIN
              - SALES_APP
              - CS_SUPPORT_CHAT
              - PARTNERS_AFFILIATE
        brands:
          type: array
          description: Список идентификаторов брендов для фильтрации
          example:
            - 1
            - 2
            - 3
          items:
            type: integer
            format: int64
        models:
          type: array
          description: Список идентификаторов моделей для фильтрации
          example:
            - 101
            - 102
            - 103
          items:
            type: integer
            format: int64
        fromDate:
          type: string
          format: date-time
          description: Начальная дата для фильтрации (включительно)
          example: 2023-01-01T00:00:00+03:00
        customerId:
          type: integer
          format: int64
          description: Идентификатор покупателя
          example: 1
        toDate:
          type: string
          format: date-time
          description: Конечная дата для фильтрации (включительно)
          example: 2023-12-31T23:59:59+03:00
        page:
          type: integer
          format: int64
          default: "0"
          description: Номер страницы для пагинации (начинается с 0)
          example: 1
          minimum: 0
        pageSize:
          type: integer
          format: int64
          default: "20"
          description: Размер страницы для пагинации
          example: 20
          minimum: 1
        typeSorting:
          $ref: "#/components/schemas/TypesSorting"
          default: DATE
          description: Используемый тип сортировки
        possibleSortingTypes:
          $ref: "#/components/schemas/SortingOptionsDto"
          description: Возможные типы сортировки
    SortingOptionsDto:
      type: object
      description: DTO for sorting options
      properties:
        sorting:
          type: array
          items:
            $ref: "#/components/schemas/SortingOption"
            description: List of available sorting options
      required:
        - sorting
    GroupingStatusQuantity:
      type: object
      description: Список группировок статусов с количеством
      properties:
        statusId:
          $ref: "#/components/schemas/DescriptionStructureEnum"
        quantity:
          type: integer
          format: int64
        description:
          type: string
    GroupingSourceQuantity:
      type: object
      description: Список группировок источников с количеством
      properties:
        sourceId:
          type: string
          description: Источники создания заявки на покупку
          enum:
            - TELEGRAM
            - WHATSAPP
            - INSTAGRAM
            - COMMENTS
            - SHOWCASE
            - CONCIERGE
            - CANCELLED_ORDERS_NOT_CONFIRMED
            - CANCELLED_ORDERS_NOT_SHIPPED
            - CANCELLED_ORDERS_NOT_APPROVED
            - PERSONAL_SHOPPING
            - PRODUCT_PAGE
            - WISHLIST
            - CART
            - O_TRENDS
            - SALES_ADMIN
            - SALES_APP
            - CS_SUPPORT_CHAT
            - PARTNERS_AFFILIATE
        quantity:
          type: integer
          format: int64
        description:
          type: string
    PurchaseOrderFilter:
      type: object
      description: Фильтр для получения заявок
      properties:
        groupingStatusQuantities:
          type: array
          description: Список группировок статусов с количеством
          items:
            $ref: "#/components/schemas/GroupingStatusQuantity"
        groupingSourceQuantities:
          type: array
          description: Список группировок источников с количеством
          items:
            $ref: "#/components/schemas/GroupingSourceQuantity"
        brands:
          type: array
          description: Список идентификаторов брендов для фильтрации
          example:
            - 1
            - 2
            - 3
          items:
            type: integer
            format: int64
        models:
          type: array
          description: Список идентификаторов моделей для фильтрации
          example:
            - 101
            - 102
            - 103
          items:
            type: integer
            format: int64
        fromDate:
          type: string
          format: date-time
          description: Начальная дата для фильтрации (включительно)
          example: 2023-01-01T00:00:00+03:00
        toDate:
          type: string
          format: date-time
          description: Конечная дата для фильтрации (включительно)
          example: 2023-12-31T23:59:59+03:00
        page:
          type: integer
          format: int64
          default: "1"
          description: Номер страницы для пагинации (начинается с 0)
          example: 1
          minimum: 1
        pageSize:
          type: integer
          format: int64
          default: "20"
          description: Размер страницы для пагинации
          example: 20
          minimum: 1
        typesSorting:
          $ref: "#/components/schemas/SortingOptionsDto"
          description: Тип сортировки
      required:
        - fromDate
        - toDate
    PurchaseOrderCreateRequest:
      type: object
      description: DTO для создания новой заявки на покупку
      properties:
        customerInfo:
          $ref: "#/components/schemas/CustomerInfoDTO"
          description: Информация о покупателе
        sourcerInfo:
          $ref: "#/components/schemas/SourcerInfoDTO"
          description: Информация о менеджере подбора предложения
        salesInfo:
          $ref: "#/components/schemas/SalesInfoDTO"
          description: Информация о менеджере по продажам
        source:
          type: string
          description: Источник создания заявки
          enum:
            - TELEGRAM
            - WHATSAPP
            - INSTAGRAM
            - COMMENTS
            - SHOWCASE
            - CONCIERGE
            - CANCELLED_ORDERS_NOT_CONFIRMED
            - CANCELLED_ORDERS_NOT_SHIPPED
            - CANCELLED_ORDERS_NOT_APPROVED
            - PERSONAL_SHOPPING
            - PRODUCT_PAGE
            - WISHLIST
            - CART
            - O_TRENDS
            - SALES_ADMIN
            - SALES_APP
            - CS_SUPPORT_CHAT
            - PARTNERS_AFFILIATE
        description:
          type: string
          description: Описание заявки
          example: Необходимо закупить офисную технику
        imagesUrl:
          type: array
          description: Ссылки на изображения
          items:
            type: string
        purchaseToNew:
          type: boolean
          default: "true"
          description: Признак создания заявки в статусе NEW
          example: true
        link:
          type: string
          description: Ссылка на товар
        bitrixDealId:
          type: integer
          format: int64
          description: Идентификатор сделки в Битрикс
          example: 123456789
      required:
        - customerInfo
        - salesInfo
        - source
        - sourcerInfo
    PersonalShopperDTO:
      type: object
      description: Информация о персональном шопере
      properties:
        id:
          type: integer
          format: int64
          description: Уникальный идентификатор шопера
          example: 12345
        userId:
          type: integer
          format: int64
          description: Уникальный идентификатор пользователя
          example: 12345
        urlAvatar:
          type: string
          description: Аватар шопера
        fio:
          type: string
          description: ФИО шопера
          example: Иванова Анна Сергеевна
          maxLength: 100
        nickname:
          type: string
          description: Никнейм шопера в системе
          example: super_shopper_22
          maxLength: 30
        paymentFormat:
          $ref: "#/components/schemas/DescriptionStructureEnum"
          description: Предпочитаемый формат оплаты
        interactionType:
          type: array
          description: Тип взаимодействия с клиентом
          items:
            $ref: "#/components/schemas/DescriptionStructureEnum"
          uniqueItems: true
        countDoneOrders:
          type: integer
          format: int64
          description: Количество завершенных заявок (со статусом DONE)
          example: 42
      required:
        - countDoneOrders
        - fio
        - id
        - interactionType
        - paymentFormat
        - urlAvatar
        - userId
    PaginatedShoppersResult:
      type: object
      properties:
        items:
          type: array
          description: Список элементов PersonalShopper для текущей страницы
          items:
            $ref: "#/components/schemas/PersonalShopperDTO"
        itemsCount:
          type: integer
          format: int64
          description: "Общее количество элементов, доступных по всем страницам (с\
            оответствующих критериям фильтрации)"
          example: 153
        totalPages:
          type: integer
          format: int64
          description: "Общее количество страниц всех элементов, соответствующих к\
            ритериям фильтрации"
          example: 10
        totalAmount:
          type: integer
          format: int64
          description: "Общее количество элементов, доступных по всем страницам (с\
            оответствующих критериям фильтрации)"
          example: 153
      required:
        - itemsCount
        - totalAmount
        - totalPages
    PersonalShopperFilter:
      type: object
      description: Фильтр для подбора персональных шоперов
      properties:
        accessSources:
          type: array
          description: Список источников доступа
          items:
            $ref: "#/components/schemas/DescriptionStructureEnum"
        categories:
          type: array
          description: Список категорий
          items:
            $ref: "#/components/schemas/DescriptionStructureEnum"
        brandIds:
          type: array
          items:
            description: ID бренда
            example: 456
            minimum: 1
          uniqueItems: true
        page:
          type: integer
          format: int32
          description: Номер страницы (начиная с 0)
          example: 0
        pageSize:
          type: integer
          format: int32
          description: Размер страницы (количество элементов на странице)
          example: 20
    Brand:
      type: object
      properties:
        id:
          type: integer
          format: int64
        brandId:
          type: integer
          format: int64
        personalShoppers:
          type: array
          items:
            $ref: "#/components/schemas/PersonalShopper"
          uniqueItems: true
    PersonalShopper:
      type: object
      properties:
        id:
          type: integer
          format: int64
        userId:
          type: integer
          format: int64
        nickname:
          type: string
        email:
          type: string
        name:
          type: string
        interactionType:
          type: array
          items:
            type: string
            description: Тип взаимодействия с системой
            enum:
              - INTERNET_SITES
              - MULTIBRAND
              - ON_REQUEST
              - UNIVERSAL
          uniqueItems: true
        paymentFormat:
          type: string
          enum:
            - PREPAYMENT
            - POSTPAYMENT
        priority:
          type: boolean
        categories:
          type: array
          items:
            $ref: "#/components/schemas/ShopperCategory"
          uniqueItems: true
        brands:
          type: array
          items:
            $ref: "#/components/schemas/Brand"
          uniqueItems: true
        accessSource:
          type: string
          enum:
            - PERSONAL_BOUTIQUE_ACCESS
            - EXCLUSIVE_SALE_AUCTION_ACCESS
        dateShopperStatus:
          type: string
          format: date-time
        bitrixId:
          type: string
    ShopperCategory:
      type: object
      properties:
        id:
          type: integer
          format: int64
        code:
          type: string
          enum:
            - CLOTHING
            - FOOTWEAR
            - BAGS_ACCESSORIES
            - WATCHES_JEWELRY
            - RARE_LIMITED
        name:
          type: string
        personalShoppers:
          type: array
          items:
            $ref: "#/components/schemas/PersonalShopper"
          uniqueItems: true
    PersonalShopperCreateRequest:
      type: object
      description: Данные для создания PersonalShopper
      properties:
        userId:
          type: integer
          format: int64
          description: Идентификатор пользователя
          example: 12345
        nickname:
          type: string
          description: Никнейм пользователя
          example: shoper_ivan
        email:
          type: string
          description: Email пользователя
          example: <EMAIL>
        name:
          type: string
          description: Полное имя пользователя
          example: Иван Иванов
        interactionType:
          type: array
          description: Тип взаимодействия
          example: "[INTERNET_SITES, MULTIBRAND, ON_REQUEST, UNIVERSAL]"
          items:
            type: string
            description: Тип взаимодействия с системой
            enum:
              - INTERNET_SITES
              - MULTIBRAND
              - ON_REQUEST
              - UNIVERSAL
          uniqueItems: true
        paymentFormat:
          type: string
          description: Формат оплаты
          enum:
            - PREPAYMENT
            - POSTPAYMENT
        categories:
          type: array
          description: Список категорий байера
          example: "[CLOTHING, FOOTWEAR]"
          items:
            type: string
            enum:
              - CLOTHING
              - FOOTWEAR
              - BAGS_ACCESSORIES
              - WATCHES_JEWELRY
              - RARE_LIMITED
          uniqueItems: true
        brands:
          type: array
          description: Список брендов
          example:
            - 1
            - 2
            - 3
          items:
            type: integer
            format: int64
          uniqueItems: true
        accessSource:
          type: string
          description: Источник доступа
          enum:
            - PERSONAL_BOUTIQUE_ACCESS
            - EXCLUSIVE_SALE_AUCTION_ACCESS
        priority:
          type: boolean
          description: Приоритетный статус
          example: true
        dateShopperStatus:
          type: string
          format: date-time
          description: Дата присвоения статуса персонального шоппера
          example: 2023-01-01T12:00:00Z
      required:
        - accessSource
        - interactionType
        - paymentFormat
        - priority
        - userId
    ShipmentOffersDTO:
      type: object
      description: Список предложений для отправки
      properties:
        shipmentId:
          type: integer
          format: int64
          description: ID товара в заявке
        offers:
          type: array
          description: Список предложений продавцов
          items:
            $ref: "#/components/schemas/OfferDTO"
    SendOffersToClientRequest:
      type: object
      properties:
        orderId:
          type: integer
          format: int64
          description: ID заявки
        offerIds:
          type: array
          description: ID предложений
          items:
            type: integer
            format: int64
        proposedOfferIds:
          type: array
          description: ID предложений от байеров
          items:
            type: integer
            format: int64
        message:
          type: string
          description: Текст сообщения
          pattern: .*\D.*
      required:
        - message
        - orderId
    PurchaseOrderUpdateRequest:
      type: object
      description: Запрос на обновление данных заявки на покупку
      properties:
        customerInfo:
          $ref: "#/components/schemas/CustomerInfoDTO"
          description: Информация о клиенте
        sourcerInfo:
          $ref: "#/components/schemas/SourcerInfoDTO"
          description: Информация о менеджере по подбору предложений
        salesInfo:
          $ref: "#/components/schemas/SalesInfoDTO"
          description: Информация о менеджере по продажам
        description:
          type: string
          description: Детальное описание заявки
          example: Нужен ноутбук для офисной работы с диагональю 15 дюймов
          maxLength: 1000
        imagesUrl:
          type: array
          description: "Ссылки на изображения, относящиеся к заявке"
          items:
            type: string
        link:
          type: string
          description: Ссылка на товар
          example: www.jacquemus.com
    SalesManagerProductDto:
      type: object
      description: DTO товара для сейлз менеджера
      properties:
        productId:
          type: integer
          format: int64
          description: Уникальный идентификатор товара c платформы
          example: 1
        seller:
          $ref: "#/components/schemas/SellerInfoDTO"
          description: DTO с информацией о продавца(шопер)
        type:
          type: string
          description: Тип товара
          enum:
            - PRODUCT_PLATFORM
            - BAYER
        price:
          type: number
          description: Цена товара
          example: 330000
        deliveryDate:
          type: string
          format: date-time
          description: Дата доставки
        validUntil:
          type: string
          format: date-time
          description: Действителен до
        bestPrice:
          type: boolean
          description: Лучшая цена
        fastDelivery:
          type: boolean
          description: Быстрая доставка
    RejectionReason:
      type: object
      properties:
        id:
          type: integer
          format: int64
        objectType:
          type: string
          enum:
            - PURCHASE_ORDER
            - PRODUCT
        orderStatus:
          type: string
          description: Статусы заявки на покупку
          enum:
            - CREATED
            - DRAFT
            - NEW
            - IN_PROGRESS_SALES
            - AWAITING_SOURCER
            - IN_PROGRESS_SOURCER
            - AWAITING_SEND_TO_CLIENT
            - AWAITING_CLIENT_ANSWER
            - REPEAT_REQUEST_TO_SALES
            - REPEAT_AWAITING_SOURCER
            - PAYED_ORDER_IN_PROGRESS
            - PAYED_REPEAT_REQUEST
            - PAYED_RR_SOURCER
            - REJECTED
            - DONE
            - CANCELLED
            - ALL
        role:
          $ref: "#/components/schemas/Roles"
        reasonText:
          type: string
        requiresDescription:
          type: boolean
    CommentListDTO:
      type: object
      properties:
        comments:
          type: array
          items:
            $ref: "#/components/schemas/CommentFullDTO"
    OrderSources:
      type: object
      description: Доступные источники заказов с их описаниями
      properties:
        sources:
          type: object
          additionalProperties:
            type: string
          description: Карта перечислений источников заказов с их человекочитаемыми
            описаниями
          example:
            ONLINE: Онлайн заказ
            PHONE: Заказ по телефону
    PurchaseOrderStateHistoryDTO:
      type: object
      description: DTO для истории переходов статусов заявки
      properties:
        id:
          type: integer
          format: int64
          description: Идентификатор истории
          example: 123
        purchaseOrderId:
          type: integer
          format: int64
          description: Идентификатор заявки
          example: 456
        userId:
          type: integer
          format: int64
          description: Идентификатор пользователя
          example: 789
        userNickName:
          type: string
          description: Ник пользователя
          example: user123
        sourceState:
          $ref: "#/components/schemas/DescriptionStructureEnum"
          description: Статус источника
          example: CREATED
        targetState:
          $ref: "#/components/schemas/DescriptionStructureEnum"
          description: Статус цели
          example: APPROVED
        transitionDate:
          type: string
          format: date-time
          description: Дата перехода
          example: 2024-05-20T12:00:00+03:00
        comment:
          type: string
          description: Комментарий
          example: Дополнительные сведения
        reasonReturn:
          type: string
          description: Причина возврата
          example: Нет в наличии
    MercauxBuyerOffers:
      description: Предложения покупателей для Mercaux
      properties:
        id:
          type: integer
          format: int64
          description: Идентификатор offer
        shopperInfo:
          $ref: "#/components/schemas/ShopperInfoDTO"
        proposedOffers:
          type: array
          description: Предложенные офферы
          items:
            $ref: "#/components/schemas/MercauxEnhancedProposedOfferDTO"
    MercauxEnhancedProposedOfferDTO:
      description: Расширенное предложение для Mercaux
      properties:
        id:
          type: integer
          format: int64
          description: Идентификатор предложения
        comparisonCriteria:
          type: array
          description: Критерии сравнения
          items:
            type: string
            description: Критерии сравнения
            enum:
              - PRICE
              - DELIVERY_DATE
          uniqueItems: true
        rublePrice:
          type: number
          description: Цена в рублях
        deliveryDate:
          type: string
          format: date-time
          description: Дата доставки
        validUntil:
          type: string
          format: date-time
          description: Срок действия
        creationDate:
          type: string
          format: date-time
          description: Дата создания
        currency:
          $ref: "#/components/schemas/CurrencyDTO"
    MercauxOfferReferenceDTO:
      description: Ссылка на оффер
      properties:
        id:
          type: integer
          format: int64
          description: Идентификатор оффера
          example: 1
    MercauxProductPlatformDTO:
      description: Продукт с платформы для Mercaux
      properties:
        offer:
          $ref: "#/components/schemas/MercauxOfferReferenceDTO"
        productId:
          type: integer
          format: int64
          description: Идентификатор товара
          example: 1
        comparisonCriteria:
          type: array
          description: Критерии сравнения
          items:
            type: string
            description: Критерии сравнения
            enum:
              - PRICE
              - DELIVERY_DATE
          uniqueItems: true
        currencyPrice:
          type: number
          description: Цена в валюте
          example: 200.0
        productPhoto:
          $ref: "#/components/schemas/ImageDTO"
        productLocation:
          type: string
          description: Расположение товара
        brand:
          type: string
          description: Бренд
        productCategory:
          type: string
          description: Категория товара
        availableSizes:
          type: array
          description: Размеры в наличии
          items:
            type: string
            description: Размеры в наличии
        priceWithoutDiscount:
          type: number
          description: Цена без скидки
        priceWithDiscount:
          type: number
          description: Цена со скидкой
        discountAmount:
          type: number
          description: Размер скидки
        sizeType:
          type: string
          description: Тип размера
        conditionId:
          type: integer
          format: int32
          description: Идентификатор состояния товара
        conditionName:
          type: string
          description: Название состояния товара
        productState:
          type: string
          description: Состояние публикации товара
        url:
          type: string
          description: URL товара
        discount:
          type: number
          description: Размер скидки в процентах
        currency:
          $ref: "#/components/schemas/CurrencyDTO"
        sellerInfo:
          $ref: "#/components/schemas/SellerInfoDTO"
    MercauxPurchaseOrderFullDTO:
      description: Полные данные о заказе на покупку для системы Mercaux
      properties:
        id:
          type: integer
          format: int64
          description: Уникальный идентификатор заказа
          example: 12345
        customer:
          $ref: "#/components/schemas/CustomerInfoDTO"
        source:
          $ref: "#/components/schemas/DescriptionStructureEnum"
        description:
          type: string
          description: Описание заказа
          example: Канцтовары для офиса на 3 квартал
        creationDate:
          type: string
          format: date-time
          description: Дата и время создания заказа
          example: 2023-07-15T10:30:45Z
        changeDate:
          type: string
          format: date-time
          description: Дата и время последнего изменения заказа
          example: 2023-07-16T14:25:30Z
        status:
          $ref: "#/components/schemas/DescriptionStructureEnum"
        sourcerInfo:
          $ref: "#/components/schemas/SourcerInfoDTO"
        salesInfo:
          $ref: "#/components/schemas/SalesInfoDTO"
        images:
          type: array
          description: "Список изображений, связанных с заказом"
          items:
            $ref: "#/components/schemas/ImageDTO"
        orders:
          type: array
          description: "Список ID заказов, включенных в этот заказ на покупку"
          items:
            type: integer
            format: int64
            description: "Список ID заказов, включенных в этот заказ на покупку"
        comments:
          type: array
          description: Список комментариев к этому заказу
          items:
            $ref: "#/components/schemas/CommentFullDTO"
        rejectionReason:
          type: string
          description: "Причина отказа в заказе, если применимо"
          example: Нет в наличии
        rejectionDescription:
          type: string
          description: "Подробное описание причины отказа, если применимо"
          example: Запрашиваемые товары в настоящее время отсутствуют на складе и
            будут доступны только в следующем месяце
        shipments:
          type: array
          description: "Список товаров, включенных в этот заказ на покупку для Mercaux"
          items:
            $ref: "#/components/schemas/MercauxShipmentResponseDTO"
        link:
          type: string
          description: Ссылка на товар
          example: www.jacquemus.com
      title: Заказ на покупку Mercaux
    MercauxShipmentResponseDTO:
      description: DTO с информацией о товаре в заявке для системы Mercaux
      properties:
        id:
          type: integer
          format: int64
          description: ID товара
          example: 1
        purchaseOrderId:
          type: integer
          format: int64
          description: "ID заявки, к которой относится товар"
          example: 100
        categoryId:
          type: integer
          format: int64
          description: ID категории товара
          example: 123
        categoryName:
          type: string
          description: Наименование категории товара
          example: Джемперы и свитеры
        brandId:
          type: integer
          format: int64
          description: ID бренда товара
          example: 456
        brandName:
          type: string
          description: Наименование бренда товара
          example: ЦВЦ СТОУНЗ
        materialAttributeId:
          type: integer
          format: int64
          description: ID атрибута материала товара
          example: 789
        materialAttributeName:
          type: string
          description: Наименование атрибута материала товара
          example: Синтетика
        colorAttributeId:
          type: integer
          format: int64
          description: ID атрибута цвета товара
          example: 101
        colorAttributeName:
          type: string
          description: Наименование атрибута цвета товара
          example: Оранжевый
        createdAt:
          type: string
          description: Дата создания записи о товаре
          example: 2023-01-01T12:00:00Z
        modelId:
          type: integer
          format: int64
          description: ID модели товара
          example: 202
        modelName:
          type: string
          description: Наименование модели товара
          example: Twist
        shipmentSize:
          $ref: "#/components/schemas/ShimpentSizeDTO"
        description:
          type: string
          description: Описание товара
          example: Кожаная куртка черного цвета
        images:
          type: array
          description: Список изображений товара
          items:
            $ref: "#/components/schemas/ImageDTO"
        links:
          type: array
          description: Ссылки на товар
          example:
            - https://example.com/product1
          items:
            type: string
            description: Ссылки на товар
            example: "[\"https://example.com/product1\"]"
        comment:
          type: string
          description: Комментарий к товару
          example: "Маленькая, бежевая"
        products:
          type: array
          description: Список продуктов с платформы
          items:
            $ref: "#/components/schemas/MercauxProductPlatformDTO"
        buyerOffers:
          type: array
          description: Предложения покупателя для Mercaux
          items:
            $ref: "#/components/schemas/MercauxBuyerOffers"
      title: Товар в заявке Mercaux
    ConciergeOrderDetailsResponse:
      description: "Детальный ответ по заказу консьержа, включает статус, детали з\
        аказа и отгрузки"
      properties:
        statusInfo:
          $ref: "#/components/schemas/StatusInfo"
        orderDetails:
          $ref: "#/components/schemas/OrderDetails"
        shipments:
          type: array
          description: Список отгрузок по заказу
          items:
            $ref: "#/components/schemas/Shipment"
    OfferApp:
      description: Информация о предложении по отгрузке
      properties:
        id:
          type: integer
          format: int64
          description: ID предложения
          example: 101
        titleTag:
          type: string
          description: Тег предложения
          example: Лучшая цена
        expirationDate:
          type: string
          format: date-time
          description: Дата истечения предложения
          example: 2024-03-20T10:30:00Z
        price:
          $ref: "#/components/schemas/Price"
        deliveredDate:
          type: string
          format: date-time
          description: Дата доставки по предложению
          example: 2024-03-25T10:30:00Z
        isExpired:
          type: boolean
          description: "Флаг, что предложение истекло"
          example: false
        isSold:
          type: boolean
          description: "Флаг, что предложение продано"
          example: false
    Order:
      description: Информация о заказе внутри
      properties:
        id:
          type: integer
          format: int64
          description: ID заказа
          example: 202
        userInfo:
          $ref: "#/components/schemas/UserInfo"
        price:
          $ref: "#/components/schemas/Price"
        deliveredDate:
          type: string
          format: date-time
          description: Дата доставки заказа
          example: 2024-03-25T10:30:00Z
    OrderDetails:
      description: Детали заказа из PurchaseOrder
      properties:
        id:
          type: integer
          format: int64
          description: ID заказа
          example: 123456
        creationDate:
          type: string
          format: date-time
          description: Дата создания заказа
          example: 2024-03-15T10:30:00Z
        description:
          type: string
          description: Описание заказа
        attachedPhotos:
          type: array
          description: Ссылки на прикрепленные фотографии
          items:
            $ref: "#/components/schemas/ImageDTO"
    Price:
      description: Цена
      properties:
        amount:
          type: number
          format: double
          description: Сумма
          example: 100.0
        currency:
          type: string
          description: Валюта
          example: USD
    Shipment:
      description: Информация об отгрузке
      properties:
        id:
          type: integer
          format: int64
          description: ID
          example: 789
        brand:
          type: string
          description: Бренд товара
          example: Nike
        size:
          $ref: "#/components/schemas/ShimpentSizeDTO"
        category:
          type: string
          description: Категория товара
        model:
          type: string
          description: Модель товара
          example: AirMax
        color:
          type: string
          description: Цвет товара
          example: Черный
        material:
          type: string
          description: Материал товара
          example: Хлопок
        link:
          type: string
          description: Ссылка на товар
          example: 'https://example.com/product/airmax   '
        description:
          type: string
          description: Описание товара
        attachedPhotos:
          type: array
          items:
            $ref: "#/components/schemas/ImageDTO"
        creationDate:
          type: string
          format: date-time
          description: Дата создания отгрузки
          example: 2024-03-15T10:30:00Z
        shipmentStatus:
          type: string
          description: Статусы товара в заказе на покупку
          enum:
            - AWAITING_PROCESSING
            - IN_PROGRESS
            - READY
            - REJECTED
        productsPlatform:
          type: array
          items:
            $ref: "#/components/schemas/OfferApp"
        ordersMono:
          type: array
          description: "Информация о заказе, к которому относится отгрузка"
          items:
            $ref: "#/components/schemas/Order"
    StatusInfo:
      description: Информация о статусе PurchaseOrder
      properties:
        status:
          type: string
          description: Код статуса
          example: IN_PROGRESS
        title:
          type: string
          description: Заголовок статуса
          example: В процессе
        description:
          type: string
          description: Описание статуса
          example: Заказ находится в обработке
    UserInfo:
      description: Информация о пользователе
      properties:
        id:
          type: integer
          format: int64
          description: ID пользователя
          example: 303
        avatarUrl:
          type: string
          description: Ссылка на аватар пользователя
          example: 'https://example.com/avatar.jpg   '
        nickname:
          type: string
          description: Никнейм пользователя
          example: john_doe
        type:
          type: string
          description: Роль пользователя
