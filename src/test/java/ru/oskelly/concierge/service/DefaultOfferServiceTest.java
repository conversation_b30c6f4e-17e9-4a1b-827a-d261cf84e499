package ru.oskelly.concierge.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.SendOffersToClientRequest;
import ru.oskelly.concierge.controller.dto.ShipmentOffersDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.enums.OfferType;
import ru.oskelly.concierge.data.repository.OfferRepository;
import ru.oskelly.concierge.data.repository.ProposedOfferRepository;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.data.repository.ShipmentRepository;
import ru.oskelly.concierge.exception.OfferNotFoundException;
import ru.oskelly.concierge.exception.ShipmentNotFoundException;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DefaultOfferServiceTest {

    private static final Long USER_ID = 123L;
    private static final Long OFFER_ID = 1L;
    private static final Long PRODUCT_ID = 23L;
    private static final Long ORDER_ID = 100L;
    private static final Long SHIPMENT_ID = 200L;
    private static final int MAX_COUNT_PRODUCT_PLATFORM = 5;

    @InjectMocks
    private DefaultOfferService offerService;

    @Mock
    private OfferRepository offerRepository;
    @Mock
    private ShipmentRepository shipmentRepository;
    @Mock
    private PurchaseOrderRepository purchaseOrderRepository;
    @Mock
    private ProposedOfferRepository proposedOfferRepository;

    @BeforeEach
    void setUp() {
        ThreadLocalContext.put(ContextConstants.USER_ID, USER_ID);
        ReflectionTestUtils.setField(offerService, "maxCountProductPlatform", MAX_COUNT_PRODUCT_PLATFORM);
    }

    @Nested
    @DisplayName("Тесты удаления продукта")
    class ProductDeletionTests {

        @Test
        @DisplayName("Успешное удаление существующего продукта")
        void deleteProduct_whenProductExists_shouldReturnNull() {
            Offer existingOffer = createTestOffer();
            when(offerRepository.findById(OFFER_ID)).thenReturn(Optional.of(existingOffer));
            doNothing().when(offerRepository).deleteById(OFFER_ID);
            assertNull(offerService.deleteProduct(OFFER_ID));
        }

        @Test
        @DisplayName("Выброс исключения OfferNotFoundException при отсутствии продукта")
        void deleteProduct_whenProductNotFound_shouldThrowException() {
            when(offerRepository.findById(OFFER_ID)).thenReturn(Optional.empty());
            assertThrows(OfferNotFoundException.class, () -> offerService.deleteProduct(OFFER_ID));
        }
    }

    @Nested
    @DisplayName("Тесты добавления продукта")
    class ProductAdditionTests {

        @Test
        @DisplayName("Проверка конфигурации лимита продуктов")
        void addingProducts_whenLimitConfigured_shouldRespectLimit() {
            ReflectionTestUtils.setField(offerService, "maxCountProductPlatform", 5);
            Object maxCount = ReflectionTestUtils.getField(offerService, "maxCountProductPlatform");
            assertInstanceOf(Integer.class, maxCount);
            assertEquals(5, (int) (Integer) maxCount);
        }
    }

    @Nested
    @DisplayName("Тесты управления покупателями")
    class ShopperManagementTests {

        @Test
        @DisplayName("Выброс исключения ShipmentNotFoundException при отсутствии отправки для добавления покупателей")
        void addingShoppers_whenShipmentNotFound_shouldThrowException() {
            ShipmentOffersDTO shipmentOffers = createShipmentOffersDTO();
            when(shipmentRepository.findById(SHIPMENT_ID)).thenReturn(Optional.empty());
            assertThrows(ShipmentNotFoundException.class, () -> offerService.addingShoppers(shipmentOffers));
        }

        @Test
        @DisplayName("Выброс исключения ShipmentNotFoundException при отсутствии отправки для добавления предложения покупателя")
        void addShopperOffer_whenShipmentNotFound_shouldThrowException() {
            ShipmentOffersDTO shipmentOffers = createShipmentOffersDTO();
            when(shipmentRepository.findById(SHIPMENT_ID)).thenReturn(Optional.empty());
            assertThrows(ShipmentNotFoundException.class, () -> offerService.addShopperOffer(shipmentOffers));
        }
    }

    @Nested
    @DisplayName("Тесты коммуникации с клиентом")
    class ClientCommunicationTests {

        @Test
        @DisplayName("Выброс исключения при отсутствии заказа на покупку")
        void sendOffersToClient_whenOrderNotFound_shouldThrowException() {
            SendOffersToClientRequest request = createSendOffersToClientRequest();
            when(purchaseOrderRepository.findById(ORDER_ID)).thenReturn(Optional.empty());
            assertThrows(ru.oskelly.concierge.exception.PurchaseOrderNotFoundException.class,
                    () -> offerService.sendOffersToClient(request));
        }
    }

    @Nested
    @DisplayName("Тесты валидации")
    class ValidationTests {

        @Test
        @DisplayName("Корректная проверка лимитов предложений")
        void createOffer_whenWithinLimits_shouldSucceed() {
            List<Offer> existingOffers = createTestOffers(2, OfferType.PLATFORM_PRODUCT);
            assertTrue(existingOffers.size() <= MAX_COUNT_PRODUCT_PLATFORM);
        }
    }

    private Offer createTestOffer() {
        return Offer.builder()
                .id(OFFER_ID)
                .userId(USER_ID)
                .sellerType("INDIVIDUAL")
                .type(OfferType.PLATFORM_PRODUCT)
                .productId(PRODUCT_ID)
                .build();
    }

    private List<Offer> createTestOffers(int count, OfferType type) {
        List<Offer> offers = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            offers.add(Offer.builder()
                    .id(OFFER_ID + i)
                    .userId(USER_ID)
                    .type(type)
                    .productId(PRODUCT_ID + i)
                    .build());
        }
        return offers;
    }

    private ShipmentOffersDTO createShipmentOffersDTO() {
        return new ShipmentOffersDTO(SHIPMENT_ID, List.of());
    }

    private SendOffersToClientRequest createSendOffersToClientRequest() {
        return new SendOffersToClientRequest(
                ORDER_ID,
                List.of(OFFER_ID),
                List.of(),
                "Test message for client"
        );
    }
}