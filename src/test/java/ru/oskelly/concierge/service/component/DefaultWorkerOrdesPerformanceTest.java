package ru.oskelly.concierge.service.component;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ru.oskelly.concierge.controller.dto.OfferPairDTO;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.PurchasePlatformStatus;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.service.PurchaseOrderService;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Тесты производительности для DefaultWorkerOrdes.
 * Проверяют работу с большими объемами данных и производительность алгоритмов.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Тесты производительности DefaultWorkerOrdes")
class DefaultWorkerOrdesPerformanceTest {

    @Mock
    private PurchaseOrderRepository purchaseOrderRepository;

    @Mock
    private PurchaseOrderService purchaseOrderService;

    @InjectMocks
    private DefaultWorkerOrdes defaultWorkerOrdes;

    private OrderInfoDTO largeOrderInfo;

    @BeforeEach
    void setUp() {
        // Создаем большой набор productIds для тестирования производительности
        Set<Long> largeProductIdSet = new HashSet<>();
        IntStream.rangeClosed(1, 1000).forEach(i -> largeProductIdSet.add((long) i));

        largeOrderInfo = new OrderInfoDTO(
                largeProductIdSet,
                10001L,
                20001L
        );
    }

    @Test
    @DisplayName("Должен обработать большое количество заказов за разумное время")
    void shouldProcessLargeNumberOfOrdersInReasonableTime() {
        // given
        List<PurchaseOrder> largeOrderList = createLargeOrderList(100); // 100 заказов
        
        when(purchaseOrderRepository.findByProductIdIn(any()))
                .thenReturn(largeOrderList);

        // when
        Instant start = Instant.now();
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(largeOrderInfo);
        Instant end = Instant.now();
        Duration processingTime = Duration.between(start, end);

        // then
        assertThat(processingTime).isLessThan(Duration.ofSeconds(5)); // Должно выполниться менее чем за 5 секунд
        assertThat(result).isNotNull();
        
        System.out.println("Время обработки 100 заказов: " + processingTime.toMillis() + " мс");
        System.out.println("Количество найденных пар: " + result.size());
    }

    @Test
    @DisplayName("Должен эффективно группировать заказы по productId")
    void shouldEfficientlyGroupOrdersByProductId() {
        // given
        List<PurchaseOrder> ordersWithManyShipments = createOrdersWithManyShipments(50, 10); // 50 заказов по 10 shipments
        
        when(purchaseOrderRepository.findByProductIdIn(any()))
                .thenReturn(ordersWithManyShipments);

        // when
        Instant start = Instant.now();
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(largeOrderInfo);
        Instant end = Instant.now();
        Duration processingTime = Duration.between(start, end);

        // then
        assertThat(processingTime).isLessThan(Duration.ofSeconds(10)); // Должно выполниться менее чем за 10 секунд
        assertThat(result).isNotNull();
        
        System.out.println("Время группировки 50 заказов с 10 shipments каждый: " + processingTime.toMillis() + " мс");
    }

    @Test
    @DisplayName("Должен обработать заказы с глубокой вложенностью структуры")
    void shouldProcessOrdersWithDeepNestedStructure() {
        // given
        List<PurchaseOrder> deepNestedOrders = createDeepNestedOrders(10, 20, 5); // 10 заказов, 20 shipments, 5 offers каждый
        
        when(purchaseOrderRepository.findByProductIdIn(any()))
                .thenReturn(deepNestedOrders);

        // when
        Instant start = Instant.now();
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(largeOrderInfo);
        Instant end = Instant.now();
        Duration processingTime = Duration.between(start, end);

        // then
        assertThat(processingTime).isLessThan(Duration.ofSeconds(15)); // Должно выполниться менее чем за 15 секунд
        assertThat(result).isNotNull();
        
        System.out.println("Время обработки глубоко вложенной структуры: " + processingTime.toMillis() + " мс");
        System.out.println("Общее количество обработанных offers: " + (10 * 20 * 5));
    }

    @Test
    @DisplayName("Должен эффективно обрабатывать множественные ProposedOffers")
    void shouldEfficientlyProcessMultipleProposedOffers() {
        // given
        List<PurchaseOrder> ordersWithManyProposedOffers = createOrdersWithManyProposedOffers(20, 50); // 20 заказов, 50 proposedOffers каждый
        
        when(purchaseOrderRepository.findByProductIdIn(any()))
                .thenReturn(ordersWithManyProposedOffers);

        // when
        Instant start = Instant.now();
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(largeOrderInfo);
        Instant end = Instant.now();
        Duration processingTime = Duration.between(start, end);

        // then
        assertThat(processingTime).isLessThan(Duration.ofSeconds(8)); // Должно выполниться менее чем за 8 секунд
        assertThat(result).isNotNull();
        
        System.out.println("Время обработки множественных ProposedOffers: " + processingTime.toMillis() + " мс");
    }

    @Test
    @DisplayName("Должен масштабироваться линейно с увеличением количества productIds")
    void shouldScaleLinearlyWithIncreasingProductIds() {
        // given
        List<PurchaseOrder> baseOrders = createLargeOrderList(50);
        when(purchaseOrderRepository.findByProductIdIn(any())).thenReturn(baseOrders);

        // Тестируем с разными размерами productIds
        int[] productIdCounts = {10, 50, 100, 500, 1000};
        List<Duration> processingTimes = new ArrayList<>();

        for (int count : productIdCounts) {
            Set<Long> productIds = new HashSet<>();
            IntStream.rangeClosed(1, count).forEach(i -> productIds.add((long) i));
            
            OrderInfoDTO orderInfo = new OrderInfoDTO(productIds, 10001L, 20001L);

            // when
            Instant start = Instant.now();
            defaultWorkerOrdes.processOrderInfo(orderInfo);
            Instant end = Instant.now();
            Duration processingTime = Duration.between(start, end);
            
            processingTimes.add(processingTime);
            System.out.println("ProductIds: " + count + ", Время: " + processingTime.toMillis() + " мс");
        }

        // then
        // Проверяем, что время не растет экспоненциально
        for (int i = 1; i < processingTimes.size(); i++) {
            Duration current = processingTimes.get(i);
            Duration previous = processingTimes.get(i - 1);
            
            // Используем наносекунды для более точного измерения
            long currentNanos = current.toNanos();
            long previousNanos = previous.toNanos();
            
            // Если предыдущее время было 0, устанавливаем минимальное значение для сравнения
            long baseTime = Math.max(previousNanos, 1000); // минимум 1 микросекунда
            
            // Время не должно увеличиваться более чем в 100 раз при увеличении данных в 10 раз
            // (более мягкое ограничение для быстрых операций)
            assertThat(currentNanos).isLessThan(baseTime * 100);
        }
    }

    // Вспомогательные методы для создания тестовых данных

    private List<PurchaseOrder> createLargeOrderList(int orderCount) {
        List<PurchaseOrder> orders = new ArrayList<>();
        
        for (int i = 1; i <= orderCount; i++) {
            PurchaseOrder order = new PurchaseOrder();
            order.setId((long) i);
            order.setCustomerId(i % 2 == 0 ? largeOrderInfo.clientId() : 99999L); // Половина с совпадающим клиентом
            order.setStatus(PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER);
            order.setOrders(new HashSet<>());
            order.setShipments(new HashSet<>());

            // Создаем shipment с несколькими offers
            Shipment shipment = createShipmentWithOffers(order, i);
            order.getShipments().add(shipment);

            orders.add(order);
        }
        
        return orders;
    }

    private List<PurchaseOrder> createOrdersWithManyShipments(int orderCount, int shipmentsPerOrder) {
        List<PurchaseOrder> orders = new ArrayList<>();
        
        for (int i = 1; i <= orderCount; i++) {
            PurchaseOrder order = new PurchaseOrder();
            order.setId((long) i);
            order.setCustomerId(largeOrderInfo.clientId());
            order.setStatus(PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER);
            order.setOrders(new HashSet<>());
            order.setShipments(new HashSet<>());

            for (int j = 1; j <= shipmentsPerOrder; j++) {
                Shipment shipment = createShipmentWithOffers(order, i * 1000 + j);
                order.getShipments().add(shipment);
            }

            orders.add(order);
        }
        
        return orders;
    }

    private List<PurchaseOrder> createDeepNestedOrders(int orderCount, int shipmentsPerOrder, int offersPerShipment) {
        List<PurchaseOrder> orders = new ArrayList<>();
        
        for (int i = 1; i <= orderCount; i++) {
            PurchaseOrder order = new PurchaseOrder();
            order.setId((long) i);
            order.setCustomerId(largeOrderInfo.clientId());
            order.setStatus(PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER);
            order.setOrders(new HashSet<>());
            order.setShipments(new HashSet<>());

            for (int j = 1; j <= shipmentsPerOrder; j++) {
                Shipment shipment = new Shipment();
                shipment.setId((long) (i * 1000 + j));
                shipment.setPurchaseOrder(order);
                shipment.setOffers(new HashSet<>());

                for (int k = 1; k <= offersPerShipment; k++) {
                    Offer offer = new Offer();
                    offer.setId((long) (i * 10000 + j * 100 + k));
                    offer.setShipment(shipment);
                    offer.setProductId((long) ((i + j + k) % 1000 + 1)); // Циклически используем productIds
                    offer.setPurchasePlatformStatus(PurchasePlatformStatus.AVAILABLE);
                    offer.setIsSentToCustomer(k % 2 == 0); // Половина отправлена клиенту
                    offer.setBitrixId((long) (i * 10000 + j * 100 + k + 50000));
                    offer.setProposedOffers(new HashSet<>());
                    
                    shipment.getOffers().add(offer);
                }

                order.getShipments().add(shipment);
            }

            orders.add(order);
        }
        
        return orders;
    }

    private List<PurchaseOrder> createOrdersWithManyProposedOffers(int orderCount, int proposedOffersPerOrder) {
        List<PurchaseOrder> orders = new ArrayList<>();
        
        for (int i = 1; i <= orderCount; i++) {
            PurchaseOrder order = new PurchaseOrder();
            order.setId((long) i);
            order.setCustomerId(largeOrderInfo.clientId());
            order.setStatus(PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER);
            order.setOrders(new HashSet<>());
            order.setShipments(new HashSet<>());

            Shipment shipment = new Shipment();
            shipment.setId((long) i);
            shipment.setPurchaseOrder(order);
            shipment.setOffers(new HashSet<>());

            Offer offer = new Offer();
            offer.setId((long) i);
            offer.setShipment(shipment);
            offer.setProductId(null); // Только proposedOffers содержат productIds
            offer.setPurchasePlatformStatus(PurchasePlatformStatus.AVAILABLE);
            offer.setIsSentToCustomer(true);
            offer.setBitrixId((long) (i + 10000));
            offer.setProposedOffers(new HashSet<>());

            for (int j = 1; j <= proposedOffersPerOrder; j++) {
                ProposedOffer proposedOffer = new ProposedOffer();
                proposedOffer.setId((long) (i * 1000 + j));
                proposedOffer.setOffer(offer);
                proposedOffer.setProposedProductId((long) ((i + j) % 1000 + 1));
                proposedOffer.setIsSentToCustomer(j % 3 == 0); // Треть отправлена клиенту
                
                offer.getProposedOffers().add(proposedOffer);
            }

            shipment.getOffers().add(offer);
            order.getShipments().add(shipment);
            orders.add(order);
        }
        
        return orders;
    }

    private Shipment createShipmentWithOffers(PurchaseOrder order, int baseId) {
        Shipment shipment = new Shipment();
        shipment.setId((long) baseId);
        shipment.setPurchaseOrder(order);
        shipment.setOffers(new HashSet<>());

        // Создаем несколько offers с разными productIds
        for (int i = 1; i <= 3; i++) {
            Offer offer = new Offer();
            offer.setId((long) (baseId * 10 + i));
            offer.setShipment(shipment);
            offer.setProductId((long) ((baseId + i) % 1000 + 1)); // Циклически используем productIds 1-1000
            offer.setPurchasePlatformStatus(PurchasePlatformStatus.AVAILABLE);
            offer.setIsSentToCustomer(i % 2 == 0); // Половина отправлена клиенту
            offer.setBitrixId((long) (baseId * 10 + i + 20000));
            offer.setProposedOffers(new HashSet<>());
            
            shipment.getOffers().add(offer);
        }

        return shipment;
    }
}
