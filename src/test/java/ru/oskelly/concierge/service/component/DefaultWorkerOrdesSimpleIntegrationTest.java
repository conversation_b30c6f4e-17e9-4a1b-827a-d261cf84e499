package ru.oskelly.concierge.service.component;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import ru.oskelly.concierge.controller.dto.OfferPairDTO;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.OfferType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.PurchasePlatformStatus;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.exception.ValidationException;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Упрощенный интеграционный тест для DefaultWorkerOrdes.
 * Использует моки для избежания проблем с базой данных и JSON типами.
 */
@SpringJUnitConfig
@DisplayName("DefaultWorkerOrdes Simple Integration Tests")
class DefaultWorkerOrdesSimpleIntegrationTest {

    private DefaultWorkerOrdes defaultWorkerOrdes;
    private PurchaseOrderRepository mockRepository;
    private ApplicationEventPublisher mockEventPublisher;

    @TestConfiguration
    static class TestConfig {
        @Bean
        @Primary
        public PurchaseOrderRepository purchaseOrderRepository() {
            return mock(PurchaseOrderRepository.class);
        }

        @Bean
        @Primary
        public ApplicationEventPublisher applicationEventPublisher() {
            return mock(ApplicationEventPublisher.class);
        }

        @Bean
        public DefaultWorkerOrdes defaultWorkerOrdes(
                PurchaseOrderRepository repository,
                ApplicationEventPublisher eventPublisher) {
            return new DefaultWorkerOrdes(repository, eventPublisher);
        }
    }

    @BeforeEach
    void setUp() {
        mockRepository = mock(PurchaseOrderRepository.class);
        mockEventPublisher = mock(ApplicationEventPublisher.class);
        defaultWorkerOrdes = new DefaultWorkerOrdes(mockRepository, mockEventPublisher);
    }

    @Test
    @DisplayName("Should validate input parameters correctly")
    void shouldValidateInputParametersCorrectly() {
        // Test null OrderInfoDTO
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(null))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("OrderInfoDTO не должен быть null");

        // Test null productIds
        OrderInfoDTO orderInfoWithNullProductIds = new OrderInfoDTO(null, 100L, 50L);
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfoWithNullProductIds))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("Список productIds не должен быть null или пустым");

        // Test empty productIds
        OrderInfoDTO orderInfoWithEmptyProductIds = new OrderInfoDTO(Set.of(), 100L, 50L);
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfoWithEmptyProductIds))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("Список productIds не должен быть null или пустым");

        // Test null clientId
        OrderInfoDTO orderInfoWithNullClientId = new OrderInfoDTO(Set.of(1L), 100L, null);
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfoWithNullClientId))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("ClientId не должен быть null");

        // Test null orderID
        OrderInfoDTO orderInfoWithNullOrderId = new OrderInfoDTO(Set.of(1L), null, 50L);
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfoWithNullOrderId))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("OrderID не должен быть null");
    }

    @Test
    @DisplayName("Should return empty list when no orders found")
    void shouldReturnEmptyListWhenNoOrdersFound() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L, 2L), 100L, 50L);
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of());

        // When
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        assertThat(result).isEmpty();
        verify(mockRepository, never()).saveAll(any());
    }

    @Test
    @DisplayName("Should mark offers as missed for non-matching customers")
    void shouldMarkOffersAsMissedForNonMatchingCustomers() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(999L, PurchaseOrderStatusEnum.NEW); // Different customer
        Shipment testShipment = createTestShipment(testOrder);
        Offer testOffer = createTestOffer(testShipment, 1L, false, 200L);
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        assertThat(result).isEmpty();
        assertThat(testOffer.getPurchasePlatformStatus()).isEqualTo(PurchasePlatformStatus.MISSED);
        
        verify(mockRepository).saveAll(any());
    }

    @Test
    @DisplayName("Should not trigger status transition for other statuses")
    void shouldNotTriggerStatusTransitionForOtherStatuses() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(100L, PurchaseOrderStatusEnum.NEW);
        Shipment testShipment = createTestShipment(testOrder);
        Offer testOffer = createTestOffer(testShipment, 1L, false, 200L);
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When
        defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        verify(mockEventPublisher, never()).publishEvent(any());
    }

    @Test
    @DisplayName("Should handle null collections gracefully")
    void shouldHandleNullCollectionsGracefully() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(100L, PurchaseOrderStatusEnum.NEW);
        testOrder.setShipments(null); // Null shipments
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When & Then
        assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(orderInfo))
                .doesNotThrowAnyException();
    }

    // Helper methods for creating test objects

    private PurchaseOrder createTestPurchaseOrder(Long customerId, PurchaseOrderStatusEnum status) {
        PurchaseOrder order = PurchaseOrder.builder()
                .id(1L)
                .customerId(customerId)
                .status(status)
                .creationDate(ZonedDateTime.now())
                .orders(new HashSet<>())
                .shipments(new HashSet<>())
                .images(new ArrayList<>()) // Empty list instead of null
                .build();
        return order;
    }

    private Shipment createTestShipment(PurchaseOrder purchaseOrder) {
        Shipment shipment = new Shipment();
        shipment.setId(1L);
        shipment.setPurchaseOrder(purchaseOrder);
        shipment.setOffers(new HashSet<>());
        
        purchaseOrder.getShipments().add(shipment);
        return shipment;
    }

    private Offer createTestOffer(Shipment shipment, Long productId, boolean isSentToCustomer, Long bitrixId) {
        Offer offer = Offer.builder()
                .id(1L)
                .shipment(shipment)
                .productId(productId)
                .type(OfferType.PLATFORM_PRODUCT)
                .purchasePlatformStatus(PurchasePlatformStatus.AVAILABLE)
                .isSentToCustomer(isSentToCustomer)
                .bitrixId(bitrixId)
                .proposedOffers(new HashSet<>())
                .creationDate(ZonedDateTime.now())
                .build();
        
        shipment.getOffers().add(offer);
        return offer;
    }

    private ProposedOffer createTestProposedOffer(Offer offer, Long proposedProductId) {
        ProposedOffer proposedOffer = new ProposedOffer();
        proposedOffer.setId(1L);
        proposedOffer.setOffer(offer);
        proposedOffer.setProposedProductId(proposedProductId);
        
        offer.getProposedOffers().add(proposedOffer);
        return proposedOffer;
    }
}