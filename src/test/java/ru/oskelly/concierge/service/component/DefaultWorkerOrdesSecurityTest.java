package ru.oskelly.concierge.service.component;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.PurchasePlatformStatus;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.exception.ValidationException;
import ru.oskelly.concierge.service.PurchaseOrderService;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

/**
 * Тесты безопасности и обработки исключений для DefaultWorkerOrdes.
 * Проверяют корректность обработки некорректных данных и защиту от атак.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Тесты безопасности DefaultWorkerOrdes")
class DefaultWorkerOrdesSecurityTest {

    @Mock
    private PurchaseOrderRepository purchaseOrderRepository;

    @Mock
    private PurchaseOrderService purchaseOrderService;

    @InjectMocks
    private DefaultWorkerOrdes defaultWorkerOrdes;

    private OrderInfoDTO validOrderInfo;

    @BeforeEach
    void setUp() {
        validOrderInfo = new OrderInfoDTO(
                Set.of(1L, 2L, 3L),
                100L,
                200L
        );
    }

    @Nested
    @DisplayName("Тесты защиты от SQL-инъекций и некорректных данных")
    class SecurityValidationTests {

        @Test
        @DisplayName("Должен безопасно обрабатывать очень большие значения ID")
        void shouldSafelyHandleVeryLargeIds() {
            // given
            OrderInfoDTO largeIdsOrderInfo = new OrderInfoDTO(
                    Set.of(Long.MAX_VALUE, Long.MAX_VALUE - 1),
                    Long.MAX_VALUE,
                    Long.MAX_VALUE
            );

            when(purchaseOrderRepository.findByProductIdIn(any()))
                    .thenReturn(Collections.emptyList());

            // when & then
            assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(largeIdsOrderInfo))
                    .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("Должен безопасно обрабатывать отрицательные значения ID")
        void shouldSafelyHandleNegativeIds() {
            // given
            OrderInfoDTO negativeIdsOrderInfo = new OrderInfoDTO(
                    Set.of(-1L, -999L, -Long.MAX_VALUE),
                    -100L,
                    -200L
            );

            when(purchaseOrderRepository.findByProductIdIn(any()))
                    .thenReturn(Collections.emptyList());

            // when & then
            assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(negativeIdsOrderInfo))
                    .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("Должен безопасно обрабатывать очень большой набор productIds")
        void shouldSafelyHandleVeryLargeProductIdSet() {
            // given - создаем набор из 10000 productIds
            Set<Long> largeProductIdSet = new HashSet<>();
            for (long i = 1; i <= 10000; i++) {
                largeProductIdSet.add(i);
            }

            OrderInfoDTO largeSetOrderInfo = new OrderInfoDTO(
                    largeProductIdSet,
                    100L,
                    200L
            );

            when(purchaseOrderRepository.findByProductIdIn(any()))
                    .thenReturn(Collections.emptyList());

            // when & then
            assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(largeSetOrderInfo))
                    .doesNotThrowAnyException();
        }
    }

    @Nested
    @DisplayName("Тесты обработки исключений в зависимостях")
    class DependencyExceptionHandlingTests {

        @Test
        @DisplayName("Должен корректно обработать исключение от репозитория")
        void shouldHandleRepositoryException() {
            // given
            when(purchaseOrderRepository.findByProductIdIn(any()))
                    .thenThrow(new RuntimeException("Ошибка базы данных"));

            // when & then
            assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(validOrderInfo))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("Ошибка базы данных");
        }

        @Nested
        @DisplayName("Тесты защиты от повреждения данных")
        class DataIntegrityTests {

            @Test
            @DisplayName("Должен защищать от изменения исходных данных")
            void shouldProtectFromModifyingOriginalData() {
                // given
                Set<Long> originalProductIds = new HashSet<>(Set.of(1L, 2L, 3L));
                OrderInfoDTO orderInfo = new OrderInfoDTO(
                        originalProductIds,
                        100L,
                        200L
                );

                when(purchaseOrderRepository.findByProductIdIn(any()))
                        .thenReturn(Collections.emptyList());

                // when
                defaultWorkerOrdes.processOrderInfo(orderInfo);

                // then
                assertThat(originalProductIds).containsExactlyInAnyOrder(1L, 2L, 3L);
                assertThat(orderInfo.productIds()).containsExactlyInAnyOrder(1L, 2L, 3L);
            }

            @Test
            @DisplayName("Должен корректно обрабатывать поврежденные связи между сущностями")
            void shouldHandleCorruptedEntityRelationships() {
                // given
                PurchaseOrder order = new PurchaseOrder();
                order.setId(1L);
                order.setCustomerId(validOrderInfo.clientId());
                order.setStatus(PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER);
                order.setOrders(new HashSet<>());

                Shipment shipment = new Shipment();
                shipment.setId(1L);
                shipment.setPurchaseOrder(null); // Поврежденная связь

                Offer offer = new Offer();
                offer.setId(1L);
                offer.setShipment(shipment);
                offer.setProductId(1L);
                offer.setPurchasePlatformStatus(PurchasePlatformStatus.AVAILABLE);
                offer.setProposedOffers(new HashSet<>());

                shipment.setOffers(Set.of(offer));
                order.setShipments(Set.of(shipment));

                when(purchaseOrderRepository.findByProductIdIn(any()))
                        .thenReturn(List.of(order));

                // when & then
                assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(validOrderInfo))
                        .doesNotThrowAnyException();
            }

            @Test
            @DisplayName("Должен корректно обрабатывать циклические ссылки")
            void shouldHandleCyclicReferences() {
                // given
                PurchaseOrder order = createTestOrder();
                order.setCustomerId(validOrderInfo.clientId());

                // Создаем потенциально проблематичную структуру
                Shipment shipment = order.getShipments().iterator().next();
                Offer offer = shipment.getOffers().iterator().next();

                // Добавляем ProposedOffer, который ссылается обратно на тот же offer
                ProposedOffer proposedOffer = new ProposedOffer();
                proposedOffer.setId(1L);
                proposedOffer.setOffer(offer);
                proposedOffer.setProposedProductId(1L);
                offer.getProposedOffers().add(proposedOffer);

                when(purchaseOrderRepository.findByProductIdIn(any()))
                        .thenReturn(List.of(order));

                // when & then
                assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(validOrderInfo))
                        .doesNotThrowAnyException();
            }
        }

        @Nested
        @DisplayName("Тесты валидации бизнес-логики")
        class BusinessLogicValidationTests {

            @Test
            @DisplayName("Должен корректно валидировать все поля ValidationException")
            void shouldCorrectlyValidateAllValidationExceptionFields() {
                // given
                OrderInfoDTO invalidOrderInfo = new OrderInfoDTO(null, null, null);

                // when & then
                assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(invalidOrderInfo))
                        .isInstanceOf(ValidationException.class)
                        .satisfies(exception -> {
                            ValidationException ve = (ValidationException) exception;
                            assertThat(ve.getMessage()).isNotBlank();
                            // Проверяем, что код ошибки установлен корректно
                            // (предполагаем, что ValidationException имеет метод getCode())
                        });
            }

            @Test
            @DisplayName("Должен предотвращать обработку заказов с некорректными статусами")
            void shouldPreventProcessingOrdersWithInvalidStatuses() {
                // given
                PurchaseOrder orderWithNullStatus = createTestOrder();
                orderWithNullStatus.setCustomerId(validOrderInfo.clientId());
                orderWithNullStatus.setStatus(null); // Некорректный статус

                when(purchaseOrderRepository.findByProductIdIn(any()))
                        .thenReturn(List.of(orderWithNullStatus));

                // when & then
                assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(validOrderInfo))
                        .doesNotThrowAnyException();

                // Проверяем, что переход статуса не был вызван для заказа с null статусом
                verifyNoInteractions(purchaseOrderService);
            }

            @Test
            @DisplayName("Должен безопасно обрабатывать заказы с некорректными customerId")
            void shouldSafelyHandleOrdersWithInvalidCustomerId() {
                // given
                PurchaseOrder orderWithNullCustomerId = createTestOrder();
                orderWithNullCustomerId.setCustomerId(null);

                when(purchaseOrderRepository.findByProductIdIn(any()))
                        .thenReturn(List.of(orderWithNullCustomerId));

                // when & then
                assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(validOrderInfo))
                        .doesNotThrowAnyException();
            }
        }

        @Nested
        @DisplayName("Тесты защиты от атак типа DoS")
        class DoSProtectionTests {

            @Test
            @DisplayName("Должен обрабатывать множественные вызовы без деградации производительности")
            void shouldHandleMultipleCallsWithoutPerformanceDegradation() {
                // given
                when(purchaseOrderRepository.findByProductIdIn(any()))
                        .thenReturn(Collections.emptyList());

                // when - выполняем множественные вызовы
                long startTime = System.currentTimeMillis();
                for (int i = 0; i < 100; i++) {
                    defaultWorkerOrdes.processOrderInfo(validOrderInfo);
                }
                long endTime = System.currentTimeMillis();
                long totalTime = endTime - startTime;

                // then
                assertThat(totalTime).isLessThan(5000); // Должно выполниться менее чем за 5 секунд
                System.out.println("Время выполнения 100 вызовов: " + totalTime + " мс");
            }
        }

        // Вспомогательные методы
        private PurchaseOrder createTestOrder() {
            PurchaseOrder order = new PurchaseOrder();
            order.setId(1L);
            order.setCustomerId(200L);
            order.setStatus(PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER);
            order.setOrders(new HashSet<>());

            Shipment shipment = new Shipment();
            shipment.setId(1L);
            shipment.setPurchaseOrder(order);

            Offer offer = new Offer();
            offer.setId(1L);
            offer.setShipment(shipment);
            offer.setProductId(1L);
            offer.setPurchasePlatformStatus(PurchasePlatformStatus.AVAILABLE);
            offer.setIsSentToCustomer(false);
            offer.setProposedOffers(new HashSet<>());

            shipment.setOffers(Set.of(offer));
            order.setShipments(Set.of(shipment));

            return order;
        }
    }
}
