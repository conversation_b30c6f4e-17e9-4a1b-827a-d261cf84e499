package ru.oskelly.concierge.service.component;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.ApplicationEventPublisher;
import ru.oskelly.concierge.controller.dto.OfferPairDTO;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.OfferType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.PurchasePlatformStatus;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.exception.ValidationException;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Интеграционный тест для класса DefaultWorkerOrdes.
 * Тестирует реальную бизнес-логику обработки заказов с использованием моков для внешних зависимостей.
 * 
 * Этот тест проверяет:
 * - Валидацию входных параметров
 * - Обработку заказов с совпадающими и несовпадающими клиентами
 * - Работу с предложенными офферами (ProposedOffer)
 * - Переходы статусов заказов
 * - Обработку граничных случаев и ошибок
 */
@DisplayName("DefaultWorkerOrdes Integration Tests")
class DefaultWorkerOrdesIntegrationTest {

    private DefaultWorkerOrdes defaultWorkerOrdes;
    private PurchaseOrderRepository mockRepository;
    private ApplicationEventPublisher mockEventPublisher;

    @BeforeEach
    void setUp() {
        mockRepository = mock(PurchaseOrderRepository.class);
        mockEventPublisher = mock(ApplicationEventPublisher.class);
        defaultWorkerOrdes = new DefaultWorkerOrdes(mockRepository, mockEventPublisher);
    }

    @Test
    @DisplayName("Should validate all input parameters correctly")
    void shouldValidateAllInputParametersCorrectly() {
        // Test null OrderInfoDTO
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(null))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("OrderInfoDTO не должен быть null");

        // Test null productIds
        OrderInfoDTO orderInfoWithNullProductIds = new OrderInfoDTO(null, 100L, 50L);
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfoWithNullProductIds))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("Список productIds не должен быть null или пустым");

        // Test empty productIds
        OrderInfoDTO orderInfoWithEmptyProductIds = new OrderInfoDTO(Set.of(), 100L, 50L);
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfoWithEmptyProductIds))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("Список productIds не должен быть null или пустым");

        // Test null clientId
        OrderInfoDTO orderInfoWithNullClientId = new OrderInfoDTO(Set.of(1L), 100L, null);
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfoWithNullClientId))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("ClientId не должен быть null");

        // Test null orderID
        OrderInfoDTO orderInfoWithNullOrderId = new OrderInfoDTO(Set.of(1L), null, 50L);
        assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfoWithNullOrderId))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("OrderID не должен быть null");
    }

    @Test
    @DisplayName("Should return empty list when no orders found in database")
    void shouldReturnEmptyListWhenNoOrdersFoundInDatabase() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L, 2L), 100L, 50L);
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of());

        // When
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        assertThat(result).isEmpty();
        verify(mockRepository, never()).saveAll(any());
    }

    @Test
    @DisplayName("Should process order and update database for matching customer")
    void shouldProcessOrderAndUpdateDatabaseForMatchingCustomer() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(100L, PurchaseOrderStatusEnum.NEW);
        Shipment testShipment = createTestShipment(testOrder);
        Offer testOffer = createTestOffer(testShipment, 1L, true, 200L);
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).bitrixId()).isEqualTo(200L);
        assertThat(result.get(0).orderId()).isEqualTo(500L);
        
        // Verify database updates
        assertThat(testOrder.getOrders()).contains(500L);
        assertThat(testOffer.getOrderId()).isEqualTo(500L);
        
        verify(mockRepository).saveAll(any());
    }

    @Test
    @DisplayName("Should mark offers as missed for non-matching customers")
    void shouldMarkOffersAsMissedForNonMatchingCustomers() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(999L, PurchaseOrderStatusEnum.NEW); // Different customer
        Shipment testShipment = createTestShipment(testOrder);
        Offer testOffer = createTestOffer(testShipment, 1L, false, 200L);
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        assertThat(result).isEmpty();
        assertThat(testOffer.getPurchasePlatformStatus()).isEqualTo(PurchasePlatformStatus.MISSED);
        
        verify(mockRepository).saveAll(any());
    }

    @Test
    @DisplayName("Should trigger status transition for AWAITING_CLIENT_ANSWER orders")
    void shouldTriggerStatusTransitionForAwaitingClientAnswerOrders() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(100L, PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER);
        Shipment testShipment = createTestShipment(testOrder);
        Offer testOffer = createTestOffer(testShipment, 1L, false, 200L);
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When
        defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        verify(mockEventPublisher).publishEvent(any());
    }

    @Test
    @DisplayName("Should not trigger status transition for orders in other statuses")
    void shouldNotTriggerStatusTransitionForOrdersInOtherStatuses() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(100L, PurchaseOrderStatusEnum.NEW);
        Shipment testShipment = createTestShipment(testOrder);
        Offer testOffer = createTestOffer(testShipment, 1L, false, 200L);
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When
        defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        verify(mockEventPublisher, never()).publishEvent(any());
    }

    @Test
    @DisplayName("Should handle null collections gracefully without errors")
    void shouldHandleNullCollectionsGracefullyWithoutErrors() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(100L, PurchaseOrderStatusEnum.NEW);
        testOrder.setShipments(null); // Null shipments collection
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When & Then
        assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(orderInfo))
                .doesNotThrowAnyException();
    }

    @Test
    @DisplayName("Should not add offer pair when bitrix ID is null")
    void shouldNotAddOfferPairWhenBitrixIdIsNull() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(100L, PurchaseOrderStatusEnum.NEW);
        Shipment testShipment = createTestShipment(testOrder);
        Offer testOffer = createTestOffer(testShipment, 1L, true, null); // null bitrix ID
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        assertThat(result).isEmpty();
        assertThat(testOffer.getOrderId()).isEqualTo(500L); // Order ID should still be set
        
        verify(mockRepository).saveAll(any());
    }

    @Test
    @DisplayName("Should not add offer pair when offer is not sent to customer")
    void shouldNotAddOfferPairWhenOfferIsNotSentToCustomer() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(100L, PurchaseOrderStatusEnum.NEW);
        Shipment testShipment = createTestShipment(testOrder);
        Offer testOffer = createTestOffer(testShipment, 1L, false, 200L); // not sent to customer
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        assertThat(result).isEmpty();
        assertThat(testOffer.getOrderId()).isEqualTo(500L); // Order ID should still be set
        
        verify(mockRepository).saveAll(any());
    }

    @Test
    @DisplayName("Should not duplicate offer pairs")
    void shouldNotDuplicateOfferPairs() {
        // Given
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 500L, 100L);
        
        PurchaseOrder testOrder = createTestPurchaseOrder(100L, PurchaseOrderStatusEnum.NEW);
        Shipment testShipment = createTestShipment(testOrder);
        
        // Create two offers with same bitrixId and productId
        Offer offer1 = createTestOffer(testShipment, 1L, true, 200L);
        Offer offer2 = createTestOffer(testShipment, 1L, true, 200L); // Same bitrixId
        
        when(mockRepository.findByProductIdIn(orderInfo.productIds()))
                .thenReturn(List.of(testOrder));

        // When
        List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(orderInfo);

        // Then
        assertThat(result).hasSize(1); // Should not duplicate
        assertThat(result.get(0).bitrixId()).isEqualTo(200L);
        assertThat(result.get(0).orderId()).isEqualTo(500L);
        
        verify(mockRepository).saveAll(any());
    }

    // Helper methods for creating test objects

    private PurchaseOrder createTestPurchaseOrder(Long customerId, PurchaseOrderStatusEnum status) {
        return PurchaseOrder.builder()
                .id(customerId) // Use customerId as ID for easier identification
                .customerId(customerId)
                .status(status)
                .creationDate(ZonedDateTime.now())
                .orders(new HashSet<>())
                .shipments(new HashSet<>())
                .images(new ArrayList<>()) // Empty list to avoid JSON issues
                .build();
    }

    private Shipment createTestShipment(PurchaseOrder purchaseOrder) {
        Shipment shipment = new Shipment();
        shipment.setId(purchaseOrder.getId() * 10 + purchaseOrder.getShipments().size()); // Unique ID
        shipment.setPurchaseOrder(purchaseOrder);
        shipment.setOffers(new HashSet<>());
        
        purchaseOrder.getShipments().add(shipment);
        return shipment;
    }

    private Offer createTestOffer(Shipment shipment, Long productId, boolean isSentToCustomer, Long bitrixId) {
        Offer offer = new Offer();
        offer.setId(shipment.getId() * 10 + shipment.getOffers().size()); // Unique ID
        offer.setShipment(shipment);
        offer.setProductId(productId);
        offer.setType(OfferType.PLATFORM_PRODUCT);
        offer.setPurchasePlatformStatus(PurchasePlatformStatus.AVAILABLE);
        offer.setIsSentToCustomer(isSentToCustomer);
        offer.setBitrixId(bitrixId);
        offer.setOrderId(0L); // Устанавливаем 0L вместо null, чтобы избежать NullPointerException
        offer.setProposedOffers(new HashSet<>());
        offer.setCreationDate(ZonedDateTime.now());
        
        shipment.getOffers().add(offer);
        return offer;
    }

    private ProposedOffer createTestProposedOffer(Offer offer, Long proposedProductId) {
        ProposedOffer proposedOffer = new ProposedOffer();
        proposedOffer.setId(offer.getId() * 10 + offer.getProposedOffers().size()); // Unique ID
        proposedOffer.setOffer(offer);
        proposedOffer.setProposedProductId(proposedProductId);
        proposedOffer.setOrderId(0L); // Устанавливаем 0L вместо null, чтобы избежать NullPointerException
        
        offer.getProposedOffers().add(proposedOffer);
        return proposedOffer;
    }
}