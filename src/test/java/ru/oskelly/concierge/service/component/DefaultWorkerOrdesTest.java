package ru.oskelly.concierge.service.component;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ru.oskelly.concierge.controller.dto.OfferPairDTO;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.PurchasePlatformStatus;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.exception.ValidationException;
import ru.oskelly.concierge.service.PurchaseOrderService;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Тесты для класса DefaultWorkerOrdes.
 * Проверяет корректность обработки информации о заказах и связанных операций.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DefaultWorkerOrdes Tests")
class DefaultWorkerOrdesTest {

    @Mock
    private PurchaseOrderRepository purchaseOrderRepository;

    @Mock
    private PurchaseOrderService purchaseOrderService;

    @InjectMocks
    private DefaultWorkerOrdes defaultWorkerOrdes;

    private OrderInfoDTO validOrderInfo;
    private PurchaseOrder testPurchaseOrder;
    private Shipment testShipment;
    private Offer testOffer;
    private ProposedOffer testProposedOffer;

    @BeforeEach
    void setUp() {
        validOrderInfo = new OrderInfoDTO(Set.of(1L, 2L), 100L, 50L);
        
        testPurchaseOrder = createTestPurchaseOrder();
        testShipment = createTestShipment();
        testOffer = createTestOffer();
        testProposedOffer = createTestProposedOffer();
        
        // Setup relationships
        testPurchaseOrder.setShipments(Set.of(testShipment));
        testShipment.setPurchaseOrder(testPurchaseOrder);
        testShipment.setOffers(Set.of(testOffer));
        testOffer.setShipment(testShipment);
        testOffer.setProposedOffers(Set.of(testProposedOffer));
        testProposedOffer.setOffer(testOffer);
    }

    @Nested
    @DisplayName("Validation Tests")
    class ValidationTests {

        @Test
        @DisplayName("Should throw exception when OrderInfo is null")
        void shouldThrowExceptionWhenOrderInfoIsNull() {
            // When & Then
            assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(null))
                    .isInstanceOf(ValidationException.class)
                    .hasMessageContaining("OrderInfoDTO не должен быть null");
        }

        @Test
        @DisplayName("Should throw exception when productIds is empty")
        void shouldThrowExceptionWhenProductIdsIsEmpty() {
            // Given
            OrderInfoDTO orderInfo = new OrderInfoDTO(Collections.emptySet(), 100L, 50L);

            // When & Then
            assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfo))
                    .isInstanceOf(ValidationException.class)
                    .hasMessageContaining("Список productIds не должен быть null или пустым");
        }

        @Test
        @DisplayName("Should throw exception when productIds is null")
        void shouldThrowExceptionWhenProductIdsIsNull() {
            // Given
            OrderInfoDTO orderInfo = new OrderInfoDTO(null, 100L, 50L);

            // When & Then
            assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfo))
                    .isInstanceOf(ValidationException.class)
                    .hasMessageContaining("Список productIds не должен быть null или пустым");
        }

        @Test
        @DisplayName("Should throw exception when clientId is null")
        void shouldThrowExceptionWhenClientIdIsNull() {
            // Given
            OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), 100L, null);

            // When & Then
            assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfo))
                    .isInstanceOf(ValidationException.class)
                    .hasMessageContaining("ClientId не должен быть null");
        }

        @Test
        @DisplayName("Should throw exception when orderID is null")
        void shouldThrowExceptionWhenOrderIdIsNull() {
            // Given
            OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(1L), null, 50L);

            // When & Then
            assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(orderInfo))
                    .isInstanceOf(ValidationException.class)
                    .hasMessageContaining("OrderID не должен быть null");
        }
    }

    @Nested
    @DisplayName("Process Order Info Tests")
    class ProcessOrderInfoTests {

        @Test
        @DisplayName("Should return empty list when no orders found")
        void shouldReturnEmptyListWhenNoOrdersFound() {
            // Given
            when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                    .thenReturn(Collections.emptyList());

            // When
            List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(validOrderInfo);

            // Then
            assertThat(result).isEmpty();
            verify(purchaseOrderRepository, never()).saveAll(any());
        }

        @Test
        @DisplayName("Should mark offer as missed for non-matching customer")
        void shouldMarkOfferAsMissedForNonMatchingCustomer() {
            // Given
            testPurchaseOrder.setCustomerId(999L); // non-matching customer
            testOffer.setProductId(1L);
            testOffer.setPurchasePlatformStatus(PurchasePlatformStatus.AVAILABLE);
            
            when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                    .thenReturn(List.of(testPurchaseOrder));

            // When
            defaultWorkerOrdes.processOrderInfo(validOrderInfo);

            // Then
            assertThat(testOffer.getPurchasePlatformStatus()).isEqualTo(PurchasePlatformStatus.MISSED);
            verify(purchaseOrderRepository).saveAll(any());
        }
    }

    @Test
    @DisplayName("Should mark proposed offer as missed for non-matching customer")
    void shouldMarkProposedOfferAsMissedForNonMatchingCustomer() {
        // Given
        testPurchaseOrder.setCustomerId(999L); // non-matching customer
        testProposedOffer.setProposedProductId(1L);
        testOffer.setPurchasePlatformStatus(PurchasePlatformStatus.AVAILABLE);
        
        when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                .thenReturn(List.of(testPurchaseOrder));

        // When
        defaultWorkerOrdes.processOrderInfo(validOrderInfo);

        // Then
        assertThat(testOffer.getPurchasePlatformStatus()).isEqualTo(PurchasePlatformStatus.MISSED);
    }

    @Test
    @DisplayName("Should add order ID to orders list")
    void shouldAddOrderIdToOrdersList() {
        // Given
        testPurchaseOrder.setCustomerId(50L); // matching customer
        testPurchaseOrder.setOrders(new HashSet<>());
        testOffer.setProductId(1L);
        
        when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                .thenReturn(List.of(testPurchaseOrder));

        // When
        defaultWorkerOrdes.processOrderInfo(validOrderInfo);

        // Then
        assertThat(testPurchaseOrder.getOrders()).contains(100L);
    }

    @Test
    @DisplayName("Should not add duplicate order ID to orders list")
    void shouldNotAddDuplicateOrderIdToOrdersList() {
        // Given
        testPurchaseOrder.setCustomerId(50L); // matching customer
        testPurchaseOrder.setOrders(new HashSet<>(Set.of(100L))); // already contains the order ID
        testOffer.setProductId(1L);
        
        when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                .thenReturn(List.of(testPurchaseOrder));

        // When
        defaultWorkerOrdes.processOrderInfo(validOrderInfo);

        // Then
        assertThat(testPurchaseOrder.getOrders()).hasSize(1);
        assertThat(testPurchaseOrder.getOrders()).contains(100L);
    }

    @Test
    @DisplayName("Should handle null collections safely")
    void shouldHandleNullCollectionsSafely() {
        // Given
        testPurchaseOrder.setShipments(null);
        
        when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                .thenReturn(List.of(testPurchaseOrder));

        // When & Then
        assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(validOrderInfo))
                .doesNotThrowAnyException();
    }

    @Nested
    @DisplayName("Status Transition Tests")
    class StatusTransitionTests {

        @Test
        @DisplayName("Should not trigger status transition for other statuses")
        void shouldNotTriggerStatusTransitionForOtherStatuses() {
            // Given
            testPurchaseOrder.setCustomerId(50L); // matching customer
            testPurchaseOrder.setStatus(PurchaseOrderStatusEnum.NEW);
            testOffer.setProductId(1L);
            
            when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                    .thenReturn(List.of(testPurchaseOrder));

            // When
            defaultWorkerOrdes.processOrderInfo(validOrderInfo);

            // Then
            verify(purchaseOrderService, never()).processOrderStatusTransitionInNewTransaction(any());
        }
    }

    @Nested
    @DisplayName("Edge Cases and Additional Coverage")
    class EdgeCasesTests {

        @Test
        @DisplayName("Should handle orders with null orders collection")
        void shouldHandleOrdersWithNullOrdersCollection() {
            // Given
            testPurchaseOrder.setCustomerId(50L);
            testPurchaseOrder.setOrders(null); // null collection
            testOffer.setProductId(1L);
            
            when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                    .thenReturn(List.of(testPurchaseOrder));

            // When
            defaultWorkerOrdes.processOrderInfo(validOrderInfo);

            // Then
            assertThat(testPurchaseOrder.getOrders()).isNotNull();
            assertThat(testPurchaseOrder.getOrders()).contains(100L);
        }

        @Test
        @DisplayName("Should handle offers with null proposed offers collection")
        void shouldHandleOffersWithNullProposedOffersCollection() {
            // Given
            testPurchaseOrder.setCustomerId(50L);
            testOffer.setProductId(1L);
            testOffer.setProposedOffers(null); // null collection
            
            when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                    .thenReturn(List.of(testPurchaseOrder));

            // When & Then
            assertThatCode(() -> defaultWorkerOrdes.processOrderInfo(validOrderInfo))
                    .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("Should handle repository exception gracefully")
        void shouldHandleRepositoryExceptionGracefully() {
            // Given
            when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                    .thenThrow(new RuntimeException("Database connection error"));

            // When & Then
            assertThatThrownBy(() -> defaultWorkerOrdes.processOrderInfo(validOrderInfo))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("Database connection error");
        }

        @Test
        @DisplayName("Should not add offer pair if bitrix ID is null")
        void shouldNotAddOfferPairIfBitrixIdIsNull() {
            // Given
            testPurchaseOrder.setCustomerId(50L);
            testOffer.setProductId(1L);
            testOffer.setIsSentToCustomer(true);
            testOffer.setBitrixId(null); // null bitrix ID
            testOffer.setOrderId(100L);
            
            when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                    .thenReturn(List.of(testPurchaseOrder));

            // When
            List<OfferPairDTO> result = defaultWorkerOrdes.processOrderInfo(validOrderInfo);

            // Then
            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("Should not mark already missed offers as missed again")
        void shouldNotMarkAlreadyMissedOffersAsMissedAgain() {
            // Given
            testPurchaseOrder.setCustomerId(999L); // non-matching customer
            testOffer.setProductId(1L);
            testOffer.setPurchasePlatformStatus(PurchasePlatformStatus.MISSED); // already missed
            
            when(purchaseOrderRepository.findByProductIdIn(validOrderInfo.productIds()))
                    .thenReturn(List.of(testPurchaseOrder));

            // When
            defaultWorkerOrdes.processOrderInfo(validOrderInfo);

            // Then
            assertThat(testOffer.getPurchasePlatformStatus()).isEqualTo(PurchasePlatformStatus.MISSED);
            // Should not save if no changes were made
            verify(purchaseOrderRepository, never()).saveAll(any());
        }
    }

    // Helper methods for creating test objects
    private PurchaseOrder createTestPurchaseOrder() {
        PurchaseOrder order = new PurchaseOrder();
        order.setId(1L);
        order.setCustomerId(50L);
        order.setStatus(PurchaseOrderStatusEnum.NEW);
        order.setOrders(new HashSet<>());
        order.setShipments(new HashSet<>());
        return order;
    }

    private Shipment createTestShipment() {
        Shipment shipment = new Shipment();
        shipment.setId(1L);
        shipment.setOffers(new HashSet<>());
        return shipment;
    }

    private Offer createTestOffer() {
        Offer offer = new Offer();
        offer.setId(1L);
        offer.setProductId(1L);
        offer.setPurchasePlatformStatus(PurchasePlatformStatus.AVAILABLE);
        offer.setIsSentToCustomer(false);
        offer.setBitrixId(200L);
        offer.setProposedOffers(new HashSet<>());
        return offer;
    }

    private ProposedOffer createTestProposedOffer() {
        ProposedOffer proposedOffer = new ProposedOffer();
        proposedOffer.setId(1L);
        proposedOffer.setProposedProductId(1L);
        proposedOffer.setIsSentToCustomer(false);
        return proposedOffer;
    }
}