package ru.oskelly.concierge.controller.miniapps;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OrderInfoDTO;
import ru.oskelly.concierge.controller.dto.miniapps.PaginatedOffersResult;
import ru.oskelly.concierge.controller.dto.miniapps.ShortOfferDTO;
import ru.oskelly.concierge.controller.dto.miniapps.filter.ShopperFilterItemsRequest;
import ru.oskelly.concierge.service.OfferService;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("Тесты контроллера офферов шоппера")
class OfferShopperControllerTest {

    @Mock
    private OfferService offerService;

    @InjectMocks
    private OfferShopperController controller;

    @Test
    @DisplayName("Получение списка офферов шоппера - успешный запрос")
    void getOffers_Success() {
        Long userId = 12345L;
        ShopperFilterItemsRequest request = new ShopperFilterItemsRequest();
        request.setPage(1);
        request.setPageLength(20);

        ShortOfferDTO shortOffer = new ShortOfferDTO(
                1L,
                new DescriptionStructureEnum("ACTIVE", "Активные", "-"),
                new DescriptionStructureEnum("NEW", "Новая заявка", "Для начала работы с заявкой ознакомьтесь с запросом пользователя и нажмите кнопку «Взять в работу»"),
                "Одежда",
                "Nike",
                new ShimpentSizeDTO("L", 1L, Set.of("L", "XL")),
                ZonedDateTime.now().plusDays(7),
                ZonedDateTime.now(),
                new ImageDTO(UUID.randomUUID(), "https://example.com/image.jpg", ZonedDateTime.now()),
                List.of(new OrderInfoDTO(101L, "PAID"))
        );

        PaginatedOffersResult expectedResult = new PaginatedOffersResult(
                List.of(shortOffer),
                1,
                1,
                1
        );

        when(offerService.getShopperOffers(eq(userId), any(ShopperFilterItemsRequest.class)))
                .thenReturn(expectedResult);

        ResponseEntity<PaginatedOffersResult> response = controller.getOffers(userId, request);

        assertThat(response.getStatusCode().value()).isEqualTo(200);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().items()).hasSize(1);
        assertThat(response.getBody().items().get(0).id()).isEqualTo(1L);
        assertThat(response.getBody().items().get(0).categoryName()).isEqualTo("Одежда");
        assertThat(response.getBody().items().get(0).brandName()).isEqualTo("Nike");
        assertThat(response.getBody().totalAmount()).isEqualTo(1);
        assertThat(response.getBody().totalPages()).isEqualTo(1);
    }

    @Test
    @DisplayName("Получение списка офферов шоппера - только обязательный параметр userId")
    void getOffers_OnlyUserId() {
        Long userId = 12345L;
        ShopperFilterItemsRequest request = new ShopperFilterItemsRequest();

        PaginatedOffersResult expectedResult = new PaginatedOffersResult(
                List.of(),
                0,
                0,
                0
        );

        when(offerService.getShopperOffers(eq(userId), any(ShopperFilterItemsRequest.class)))
                .thenReturn(expectedResult);

        ResponseEntity<PaginatedOffersResult> response = controller.getOffers(userId, request);

        assertThat(response.getStatusCode().value()).isEqualTo(200);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().items()).isEmpty();
        assertThat(response.getBody().totalAmount()).isEqualTo(0);
    }

    @Test
    @DisplayName("Получение списка офферов шоппера - с фильтрами")
    void getOffers_WithFilters() {
        Long userId = 12345L;
        ShopperFilterItemsRequest request = new ShopperFilterItemsRequest();
        request.setPage(1);
        request.setPageLength(20);

        PaginatedOffersResult expectedResult = new PaginatedOffersResult(
                List.of(),
                0,
                0,
                0
        );

        when(offerService.getShopperOffers(eq(userId), any(ShopperFilterItemsRequest.class)))
                .thenReturn(expectedResult);

        ResponseEntity<PaginatedOffersResult> response = controller.getOffers(userId, request);

        assertThat(response.getStatusCode().value()).isEqualTo(200);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().items()).isEmpty();
    }
}
