# Конфигурация для тестовой среды
spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
        globally_quoted_identifiers: false
  
  h2:
    console:
      enabled: true
  
  # Отключаем Flyway для тестов с H2
  flyway:
    enabled: false
  
  # Отключаем ненужные для тестов компоненты
  cloud:
    config:
      enabled: false
  
  # Логирование для тестов
  logging:
    level:
      ru.oskelly.concierge: DEBUG
      org.springframework.transaction: DEBUG
      org.hibernate.SQL: DEBUG
      org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Настройки для тестирования производительности
test:
  performance:
    timeout-seconds: 30
    max-processing-time-ms: 5000

# Настройки для внешних сервисов (заглушки для тестов)
bitrix:
  url: http://localhost:8080/mock-bitrix
