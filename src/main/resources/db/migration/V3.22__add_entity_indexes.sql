-- Add indexes for PurchaseOrder entity
CREATE INDEX IF NOT EXISTS idx_purchase_order_customer_status ON purchase_order (customer_id, status);
CREATE INDEX IF NOT EXISTS idx_purchase_order_status ON purchase_order (status);

-- Add indexes for Offer entity
CREATE INDEX IF NOT EXISTS idx_offer_product_id ON offer (product_id);
CREATE INDEX IF NOT EXISTS idx_offer_sent_to_customer ON offer (is_sent_to_customer);
CREATE INDEX IF NOT EXISTS idx_offer_bitrix_order ON offer (bitrix_id, order_id);
CREATE INDEX IF NOT EXISTS idx_offer_platform_status ON offer (purchase_platform_status);
CREATE INDEX IF NOT EXISTS idx_offer_shipment_id ON offer (shipment_id);

-- Add indexes for ProposedOffer entity
CREATE INDEX IF NOT EXISTS idx_proposed_offer_product_id ON proposed_offer (proposed_product_id);
CREATE INDEX IF NOT EXISTS idx_proposed_offer_offer_id ON proposed_offer (offer_id);
CREATE INDEX IF NOT EXISTS idx_proposed_offer_order_id ON proposed_offer (order_id);

-- Add indexes for Shipment entity
CREATE INDEX IF NOT EXISTS idx_shipment_purchase_order ON shipment (purchase_order_id);