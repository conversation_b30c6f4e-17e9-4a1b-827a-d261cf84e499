package ru.oskelly.concierge.kafka.listener;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.kafka.dto.ClientOfferWhatsAppSendResultMessage;
import ru.oskelly.concierge.service.OfferStatusService;
import ru.oskelly.concierge.service.PurchaseOrderService;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

/**
 * Обработка результата отправки предложений клиенту в WhatsApp
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ClientOfferWhatsAppSendResultListener {

    private final PurchaseOrderService purchaseOrderService;
    private final OfferStatusService offerStatusService;

    @KafkaListener(
        topics = "${kafka.topics.offer-message-client-send-request-result.name}",
        autoStartup = "${kafka.topics.offer-message-client-send-request-result.enabled}"
    )
    public void handleEvent(@Payload @Valid ClientOfferWhatsAppSendResultMessage message, Acknowledgment ack) {
        if (!message.success()) {
            log.error("Ошибка при отправке предложений в WhatsApp: {}", message.error());
            ack.acknowledge();
            return;
        }

        ThreadLocalContext.put(ContextConstants.USER_ID, message.userId());
        ThreadLocalContext.put(ContextConstants.COMMENT, "Отправлено сообщение: " + message.text());

        purchaseOrderService.eventProcessing(
            message.orderId(),
            PurchaseOrderTransitionEvent.SEND_PURPOSAL_TO_CLIENT,
            message.role()
        );

        offerStatusService.setOffersAsSentToCustomer(message.offerIds(), message.proposedOfferIds());

        ack.acknowledge();
    }
}
