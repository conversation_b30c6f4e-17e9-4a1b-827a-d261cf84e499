package ru.oskelly.concierge.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Событие для обработки перехода статуса заказа.
 * Используется для разрыва циклической зависимости между сервисами.
 */
@Getter
public class OrderStatusTransitionEvent extends ApplicationEvent {
    
    private final Long orderId;
    
    public OrderStatusTransitionEvent(Object source, Long orderId) {
        super(source);
        this.orderId = orderId;
    }
}