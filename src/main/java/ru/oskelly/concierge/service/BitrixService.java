package ru.oskelly.concierge.service;

import jakarta.annotation.Nullable;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseRequest;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseResponse;
import ru.oskelly.concierge.bitrix.dto.BitrixContactDTO;
import ru.oskelly.concierge.bitrix.dto.DealResponse;
import ru.oskelly.concierge.bitrix.dto.DealStatus;
import ru.oskelly.concierge.bitrix.dto.User;

import java.util.List;

public interface BitrixService {
    /**
     * Создание сделки в Bitrix
     *
     * @param request запрос с данными для создания сделки
     * @return ответ с идентификатором созданной сделки
     */
    BitrixBaseResponse<Long> createDeal(BitrixBaseRequest request);

    /**
     * Получение списка контактов из Bitrix
     *
     * @param phone для получения контактов
     * @return ответ с списком контактов
     */
    BitrixBaseResponse<List<BitrixContactDTO>> listContacts(String phone);

    /**
     * Создание контакта в Bitrix
     *
     * @param request запрос с данными для создания контакта
     * @return ответ с идентификатором созданного контакта
     */
    BitrixBaseResponse<Long> createContact(BitrixBaseRequest request);

    /**
     * Поиск chatId в Bitrix по фильтру
     *
     * @param entityId идентификатор сущности (например, контакт или сделка)
     * @return идентификатор найденного чата или null, если чат не найден
     */
    @Nullable
    Long getChatId(Long entityId);

    void sendMessageByChatId(Long chatId, String message);

    void sendMessageByPhoneNumber(Long phone, String message);

    void sendOffersToBitrixOpenLine(String phone, String text, Long chatId);

    Long getCrmEntityId(String phone);

    /**
     * Обновление статуса сделки в Bitrix
     *
     * @param dealId     идентификатор сделки
     * @param dealStatus новый статус сделки
     */
    void updateBitrixDealStatus(Long dealId, DealStatus dealStatus);

    /**
     * Получение информации о сделке из Bitrix
     *
     * @param dealId идентификатор сделки
     * @return ответ с информацией о сделке
     */
    DealResponse getBitrixDeal(Long dealId);

    /**
     * Поиск пользователя в Bitrix по email
     *
     * @param email адрес электронной почты
     * @return данные пользователя
     */
    User findUserByEmail(String email);

    /**
     * Добавить пользователя к сделке в Bitrix
     *
     * @param userId id пользователя в Bitrix
     * @param dealId идентификатор сделки в Bitrix
     */
    void assignUserToDeal(Long userId, Long dealId);
}
