package ru.oskelly.concierge.service;

import ru.oskelly.concierge.controller.dto.ProposedOfferDTO;
import ru.oskelly.concierge.controller.dto.SendOffersToClientRequest;
import ru.oskelly.concierge.controller.dto.ShipmentOffersDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.controller.dto.miniapps.PaginatedOffersResult;
import ru.oskelly.concierge.controller.dto.miniapps.filter.ShopperFilterItemsRequest;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;

import java.util.Set;

public interface OfferService {
    /**
     * Добавление товара
     *
     * @param shipmentOffers - DTO с данными товара
     * @return - DTO с данными предложения
     */
    ShipmentOffersDTO addingProducts(ShipmentOffersDTO shipmentOffers);

    /**
     * Добавление шоперов
     *
     * @param shipmentOffers - DTO с данными шоперов
     * @return - DTO с данными предложения
     */
    ShipmentOffersDTO addingShoppers(ShipmentOffersDTO shipmentOffers);

    /**
     * Удаление товара
     *
     * @param offerId - идентификатор товара
     * @return - пустой ответ
     */
    Void deleteProduct(Long offerId);

    /**
     * Добавление предложения шопера
     *
     * @param shipmentOffers - набор предложений
     * @return - набор обновленных данных
     */
    ShipmentOffersDTO addShopperOffer(ShipmentOffersDTO shipmentOffers);

    /**
     * Отправка предложений клиенту в WhatsApp
     *
     * @param request предложения для отправки
     */
    void sendOffersToClient(SendOffersToClientRequest request);

    /**
     * Получение предложений по идентификатору оффера
     *
     * @param offerId - идентификатор оффера
     * @return - список предложений
     */
    Set<ProposedOfferDTO> getProposedByOfferId(Long offerId);

    /**
     * Получение деталей оффера для мини-приложений
     *
     * @param offerId - идентификатор оффера
     * @return - детали оффера
     */
    OfferDetailsDTO getOfferDetails(Long offerId);

    /**
     * Получение списка офферов шоппера с фильтрацией по статусам и пагинацией
     *
     * @param userId - идентификатор пользователя (шоппера)
     * @param request - запрос с фильтрами и пагинацией
     * @return - пагинированный результат с офферами
     */
    PaginatedOffersResult getShopperOffers(Long userId, ShopperFilterItemsRequest request);
}
