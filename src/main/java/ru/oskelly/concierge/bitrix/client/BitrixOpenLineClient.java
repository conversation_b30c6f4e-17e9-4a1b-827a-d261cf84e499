package ru.oskelly.concierge.bitrix.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import ru.oskelly.concierge.bitrix.config.BitrixOpenLineFeignConfig;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseResponse;
import ru.oskelly.concierge.bitrix.dto.BitrixChatResponse;
import ru.oskelly.concierge.bitrix.dto.ImopenlinesCrmChatGetLastIdRequest;
import ru.oskelly.concierge.bitrix.dto.SendClientOfferBitrixMessageRequest;

import java.util.List;

@FeignClient(
    name = "oneBitClient",
    url = "${bitrix-open-line.url}",
    configuration = BitrixOpenLineFeignConfig.class
)
public interface BitrixOpenLineClient {

    /**
     * Получает последний идентификатор чата в OpenLines для указанной CRM-сущности (например, сделки).
     *
     * @param request объект запроса, содержащий тип и ID CRM-сущности (например, сделка),
     * @return ID последнего найденного чата (или null, если не найден)
     */
    @PostMapping(
        value = "imopenlines.crm.chat.get",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<List<BitrixChatResponse>> getChatId(@RequestBody ImopenlinesCrmChatGetLastIdRequest request);

    /**
     * Отправляет сообщение в чат по его идентификатору через Bitrix24 OpenLines.
     *
     * @param request объект запроса, содержащий ID чата и содержимое сообщения (текст, вложения и т. д.)
     * @return ID отправленного сообщения (или null, если не удалось отправить)
     */
    @PostMapping(
            value = "imopenlines.sendMessageByChatId",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<Void> sendMessageByChatId(@RequestBody SendClientOfferBitrixMessageRequest request);

    /**
     * Отправляет сообщение в чат по его номеру телефона через Bitrix24 OpenLines.
     *
     * @param request объект запроса, содержащий номер телефона и содержимое сообщения (текст, вложения и т. д.)
     * @return ID отправленного сообщения (или null, если не удалось отправить)
     */
    @PostMapping(
            value = "imopenlines.sendMessageByPhoneNumber",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    BitrixBaseResponse<Void> sendMessageByPhoneNumber(@RequestBody SendClientOfferBitrixMessageRequest request);
}
