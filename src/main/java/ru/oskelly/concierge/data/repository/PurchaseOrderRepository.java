package ru.oskelly.concierge.data.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.QPurchaseOrder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface PurchaseOrderRepository extends BaseRepository<PurchaseOrder, QPurchaseOrder, Long> {

    @NonNull
    @EntityGraph(value = "PurchaseOrder.withShipments", type = EntityGraph.EntityGraphType.LOAD)
    Optional<PurchaseOrder> findById(@NonNull Long id);

    @EntityGraph(value = "PurchaseOrder.withShipments", type = EntityGraph.EntityGraphType.LOAD)
    Optional<PurchaseOrder> findByIdAndCustomerId(Long id, Long customerId);

    @EntityGraph(value = "PurchaseOrder.withShipmentsAndOffers", type = EntityGraph.EntityGraphType.LOAD)
    Optional<PurchaseOrder> findWithOffersById(Long id);

    @EntityGraph(value = "PurchaseOrder.withShipmentsAndOffers", type = EntityGraph.EntityGraphType.LOAD)
    Optional<PurchaseOrder> findWithOffersByIdAndCustomerId(Long id, Long customerId);

    @Query("SELECT p.id FROM PurchaseOrder p WHERE p.salesId = :salesId AND p.status IN :statuses")
    List<Long> findBySalesIdAndStatusIn(Long salesId, Set<PurchaseOrderStatusEnum> statuses);

    @Query("SELECT p.id FROM PurchaseOrder p WHERE p.sourcerId = :sourcerId AND p.status IN :statuses")
    List<Long> findBySourcerIdAndStatusIn(Long sourcerId, Set<PurchaseOrderStatusEnum> statuses);

    boolean existsByIdAndSalesIdNotNull(Long orderId);

    boolean existsByIdAndSourcerIdNotNull(Long orderId);

    List<PurchaseOrder> findByCustomerId(Long customerId);

    Optional<PurchaseOrder> findByBitrixDealId(Long dealId);

    Page<PurchaseOrder> findByStatusIn(Set<PurchaseOrderStatusEnum> statuses, Pageable pageable);

    PurchaseOrder findByShipments_Id(Long id);

    @EntityGraph(value = "PurchaseOrder.withShipmentsAndOffers", type = EntityGraph.EntityGraphType.LOAD)
    @Query("SELECT DISTINCT p FROM PurchaseOrder p " +
            "LEFT JOIN p.shipments s " +
            "LEFT JOIN s.offers o " +
            "LEFT JOIN o.proposedOffers po " +
            "WHERE p.customerId = :customerId " +
            "AND (o.productId = :productId OR (o.productId IS NULL AND po.proposedProductId = :productId))")
    List<PurchaseOrder> findByCustomerIdAndProductId(Long customerId, Long productId);

    @EntityGraph(value = "PurchaseOrder.withShipmentsAndOffers", type = EntityGraph.EntityGraphType.LOAD)
    @Query("SELECT DISTINCT p FROM PurchaseOrder p " +
            "LEFT JOIN p.shipments s " +
            "LEFT JOIN s.offers o " +
            "LEFT JOIN o.proposedOffers po " +
            "WHERE p.customerId = :customerId " +
            "AND (o.productId IN :productIds OR (o.productId IS NULL AND po.proposedProductId IN :productIds))")
    List<PurchaseOrder> findByCustomerIdAndProductIdIn(Long customerId, Set<Long> productIds);

    @Query("""
        SELECT DISTINCT po FROM PurchaseOrder po
        JOIN FETCH po.shipments s
        JOIN FETCH s.offers o
        LEFT JOIN FETCH o.proposedOffers prop
        WHERE o.productId IN :productIds OR prop.proposedProductId IN :productIds
        """)
    List<PurchaseOrder> findByProductIdIn(@Param("productIds") Set<Long> productIds);

}