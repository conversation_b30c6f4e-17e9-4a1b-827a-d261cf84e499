package ru.oskelly.concierge.data.model.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.ResourceBundle;

@Schema(description = "Статусы заявки на покупку")
@Getter
public enum PurchaseOrderStatusEnum {
    @Schema(description = "Заявка создана, но еще не обработана")
    CREATED("Создана", 777),
    @Schema(description = "Черновик заявки (не завершенное оформление)")
    DRAFT("Черновик заявки", 777),
    @Schema(description = "Новая заявка, ожидает обработки")
    NEW("Новая заявка", 1),
    @Schema(description = "Заявка в работе у менеджера по продажам")
    IN_PROGRESS_SALES("В работе", 2),
    @Schema(description = "Заявка ожидает передачи специалисту по поиску (сорсеру)")
    AWAITING_SOURCER("Ожидает передачи сорсеру", 3),
    @Schema(description = "Заявка в работе у сорсера")
    IN_PROGRESS_SOURCER("В работе у сорсера", 4),
    @Schema(description = "Заявка готова и ожидает отправки клиенту")
    AWAITING_SEND_TO_CLIENT("Ожидает отправки клиенту", 6),
    @Schema(description = "Заявка отправлена клиенту, ожидается его ответ")
    AWAITING_CLIENT_ANSWER("Ожидает ответа клиента", 7),
    @Schema(description = "Требуется повторная обработка менеджером по продажам")
    REPEAT_REQUEST_TO_SALES("Повторный запрос к Sales", 777),
    @Schema(description = "Требуется повторная обработка сорсером")
    REPEAT_AWAITING_SOURCER("Повторное ожидание сорсера", 777),
    @Schema(description = "Оплаченный заказ находится в работе")
    PAYED_ORDER_IN_PROGRESS("Оплаченный заказ в работе", 777),
    @Schema(description = "Оплаченный повторный запрос")
    PAYED_REPEAT_REQUEST("Оплаченный повторный запрос", 777),
    @Schema(description = "Оплаченный повторный запрос сорсеру")
    PAYED_RR_SOURCER("Оплаченный повторный запрос сорсеру", 777),
    @Schema(description = "Заявка отклонена")
    REJECTED("Отклонена", 999),
    @Schema(description = "Заявка успешно завершена")
    DONE("Завершена", 12),
    @Schema(description = "Заявка отменена")
    CANCELLED("Отменена", 13),
    @Schema(description = "Все статусы")
    ALL("Все", -0);

    @Schema(description = "Человеко-читаемое описание статуса")
    private final String defaultDescription;
    @Schema(description = "Порядковый номер статуса")
    private final int order;

    PurchaseOrderStatusEnum(String defaultDescription, int order) {
        this.defaultDescription = defaultDescription;
        this.order = order;
    }

    public String getDescription(Roles role) {
        if (role == null) {
            return defaultDescription;
        }
        try {
            String bundleName = "localization." + role.name().toLowerCase() + "_status";
            ResourceBundle bundle = ResourceBundle.getBundle(bundleName);
            return bundle.getString(this.name());
        } catch (Exception e) {
            return defaultDescription;
        }
    }

    public String getStatusInfo(Roles role) {
        if (role == null) {
            return "-";
        }
        try {
            String bundleName = "localization." + role.name().toLowerCase() + "_status_info";
            ResourceBundle bundle = ResourceBundle.getBundle(bundleName);
            return bundle.getString(this.name());
        } catch (Exception e) {
            return "-";
        }
    }

    public boolean isSynthetic() {
        return this == ALL;
    }
}