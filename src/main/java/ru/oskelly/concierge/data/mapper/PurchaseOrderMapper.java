package ru.oskelly.concierge.data.mapper;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.OrdersForConciergeDTO;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFullDTO;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = ShipmentMapper.class)
public interface PurchaseOrderMapper {
    @Mapping(target = "salesInfo.salesId", source = "salesId")
    @Mapping(target = "sourcerInfo.sourcerId", source = "sourcerId")
    @Mapping(target = "customer.customerId", source = "customerId")
    @Mapping(target = "status", source = "status", qualifiedByName = "statusToDescriptionStructure")
    PurchaseOrderFullDTO toFullDto(PurchaseOrder purchaseOrder, @Context PersonalShopperRepository personalShopperRepository);

    @Mapping(target = "salesId", source = "salesInfo.salesId")
    @Mapping(target = "sourcerId", source = "sourcerInfo.sourcerId")
    @Mapping(target = "customerId", source = "customer.customerId")
    @Mapping(target = "status", source = "status", qualifiedByName = "descriptionStructureToStatus")
    PurchaseOrder toEntity(PurchaseOrderFullDTO dto);

    @Mapping(target = "shipments", qualifiedByName = "toResponseDTOWithoutOffers")
    @Mapping(target = "salesInfo.salesId", source = "salesId")
    @Mapping(target = "sourcerInfo.sourcerId", source = "sourcerId")
    @Mapping(target = "customer.customerId", source = "customerId")
    @Mapping(target = "status", source = "status", qualifiedByName = "statusToDescriptionStructure")
    PurchaseOrderFullDTO toFullDtoWithoutOffers(PurchaseOrder purchaseOrder);

    @Mapping(target = "customerId", source = "customerInfo.customerId")
    @Mapping(target = "customerNickName", source = "customerInfo.customerNickName")
    @Mapping(target = "customerPhone", source = "customerInfo.customerPhone")
    @Mapping(target = "salesId", source = "salesInfo.salesId")
    @Mapping(target = "status", source = "status", qualifiedByName = "descriptionStructureToStatus")
    PurchaseOrder toEntity(OrdersForConciergeDTO ordersForConciergeDTO);

    @Named("statusToDescriptionStructure")
    default DescriptionStructureEnum statusToDescriptionStructure(PurchaseOrderStatusEnum status) {
        if (status == null) {
            return null;
        }
        
        try {
            Roles roles = ThreadLocalContext.get(ContextConstants.ROLE, Roles.class);
            return new DescriptionStructureEnum(status.name(), status.getDescription(roles), status.getStatusInfo(roles));
        } catch (Exception e) {
            return new DescriptionStructureEnum(status.name(), status.getDescription(null), status.getStatusInfo(null));
        }
    }

    @Named("descriptionStructureToStatus")
    default PurchaseOrderStatusEnum descriptionStructureToStatus(DescriptionStructureEnum descriptionStructure) {
        if (descriptionStructure == null || descriptionStructure.code() == null) {
            return null;
        }
        
        try {
            return PurchaseOrderStatusEnum.valueOf(descriptionStructure.code());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
