package ru.oskelly.concierge.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import ru.oskelly.concierge.controller.dto.OfferPairDTO;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.controller.dto.OrderSources;
import ru.oskelly.concierge.controller.dto.PaginatedResult;
import ru.oskelly.concierge.controller.dto.ProposedSendClientDTO;
import ru.oskelly.concierge.controller.dto.PurchaseOrderCreateRequest;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFilter;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFullDTO;
import ru.oskelly.concierge.controller.dto.PurchaseOrderUpdateRequest;
import ru.oskelly.concierge.controller.dto.PurchaseOrderWithShipmentsCreateRequest;
import ru.oskelly.concierge.controller.dto.RejectionEventRequest;
import ru.oskelly.concierge.controller.dto.RequestsFilter;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import java.util.List;

@Validated
@Tag(name = "concierge-purchase-order-controller", description = "API для взаимодействия с заявками на покупку")
@RequestMapping("/api/v1/purchase")
public interface PurchaseOrderApiDelegate {

    @Operation(
            operationId = "createPurchaseOrder",
            summary = "Создание заявки на покупку",
            description = "Создание заявки на покупку и старт бизнес-процесса",
            responses = @ApiResponse(responseCode = "200", description = "Заявка успешно создана", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseOrderFullDTO.class))
            })
    )
    @PostMapping(
            value = "/create",
            produces = {"application/json"}
    )
    default ResponseEntity<PurchaseOrderFullDTO> createPurchaseOrder(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Запрос с данными для создания заявки",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseOrderCreateRequest.class)))
            @RequestBody @Valid PurchaseOrderCreateRequest createRequest,
            @Parameter(name = "userId", description = "ID пользователя", required = false, in = ParameterIn.QUERY)
            @RequestParam(name = "userId", required = false) Long userId) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "createPurchaseOrderWithShipments",
            summary = "Создание заявки на покупку со списком товаров",
            description = "Создание заявки на покупку и старт бизнес-процесса",
            responses = @ApiResponse(
                    responseCode = "200",
                    description = "Заявка успешно создана",
                    content = {
                            @Content(
                                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                                    schema = @Schema(implementation = PurchaseOrderFullDTO.class)
                            )
                    }
            )
    )
    @PostMapping(
            value = "/purchase/with-shipments/create",
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    default ResponseEntity<PurchaseOrderFullDTO> createPurchaseOrderWithShipments(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Запрос с данными для создания заявки",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = PurchaseOrderWithShipmentsCreateRequest.class)
                    )
            )
            @RequestBody @Valid PurchaseOrderWithShipmentsCreateRequest createRequest,

            @Parameter(
                    name = "userId",
                    description = "ID пользователя",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam(name = "userId") Long userId
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "changeStateOperation",
            summary = "Изменение статуса заявки",
            description = "Изменение статуса заявки",
            responses = @ApiResponse(responseCode = "200", description = "Состояние успешно изменено", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseOrderFullDTO.class))
            })
    )
    @PostMapping(
            value = "/{orderId}/transition",
            produces = {"application/json"}
    )
    default ResponseEntity<PurchaseOrderFullDTO> eventSend(
            @Parameter(name = "orderId", description = "ID заявки на покупку", required = true, in = ParameterIn.PATH)
            @PathVariable Long orderId,
            @Parameter(name = "eventCode", description = "Обрабатываемое событие", required = true, in = ParameterIn.QUERY)
            @RequestParam("eventCode") PurchaseOrderTransitionEvent eventCode,
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @Parameter(name = "role", description = "Роль пользователя", in = ParameterIn.QUERY)
            @RequestParam(name = "role", required = false) Roles role) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "getPurchaseOrder",
            summary = "Получение заявки на покупку по ID",
            description = "Получение заявки на покупку по ID",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Заявка успешно получена",
                            content = {@Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseOrderFullDTO.class))}),
                    @ApiResponse(responseCode = "404", description = "Заявка не найдена")
            }
    )
    @GetMapping(
            value = "/{orderId}",
            produces = {"application/json"}
    )
    default ResponseEntity<PurchaseOrderFullDTO> getPurchaseOrder(
            @Parameter(name = "orderId", description = "ID заявки", required = true, in = ParameterIn.PATH)
            @PathVariable Long orderId,
            @Parameter(name = "role", description = "Роль пользователя",
                    schema = @Schema(implementation = Roles.class), in = ParameterIn.QUERY)
            @RequestParam(name = "role", required = false) Roles role,
            @Parameter(name = "customerId", description = "ID пользователя", in = ParameterIn.QUERY)
            @RequestParam(name = "customerId", required = false) Long customerId) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @PostMapping(value = "/{orderId}/rejection", produces = "application/json")
    @Operation(
            summary = "Создание события отклонения",
            description = "Добавляет событие отклонения к заявке.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Заявка успешно получена",
                            content = {@Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseOrderFullDTO.class))}),
                    @ApiResponse(responseCode = "404", description = "Заявка или причина отклонения не найдена")
            }
    )
    default ResponseEntity<PurchaseOrderFullDTO> createRejectionEvent(
            @Parameter(description = "Данные для создания события отклонения", required = true)
            @RequestBody RejectionEventRequest request,
            @Parameter(
                    name = "userId",
                    description = "Идентификатор пользователя, выполняющего отклонение",
                    example = "789",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam Long userId,
            @Parameter(
                    name = "orderId",
                    description = "Идентификатор заявки",
                    example = "789",
                    required = true,
                    in = ParameterIn.PATH
            )
            @PathVariable Long orderId
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @PatchMapping(
            value = "/{orderId}",
            produces = {"application/json"}
    )
    default ResponseEntity<PurchaseOrderFullDTO> updatePurchaseOrder(
            @Parameter(name = "orderId", description = "ID заявки", required = true, in = ParameterIn.PATH)
            @PathVariable Long orderId,
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Запрос с данными для обновления заявки",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseOrderUpdateRequest.class)))
            @RequestBody PurchaseOrderUpdateRequest updateRequest) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @PostMapping(
            value = "/filter",
            produces = {"application/json"}
    )
    @Operation(
            summary = "Получение данных для фильтрации заявок",
            description = "Формирование фильтра для получения заявок",
            responses = {
                    @ApiResponse(responseCode = "200", description = "OK",
                            content = {@Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseOrderFilter.class))}),
                    @ApiResponse(responseCode = "400", description = "Bad Request")
            }
    )
    default ResponseEntity<PurchaseOrderFilter> getFilterPurchaseOrders(
            @Parameter(
                    name = "filter",
                    description = "Фильтр для получения заявок",
                    required = true,
                    schema = @Schema(implementation = PurchaseOrderFilter.class)
            )
            @RequestBody PurchaseOrderFilter filter,
            @Parameter(
                    name = "userId",
                    description = "Идентификатор пользователя",
                    example = "789",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam Long userId,
            @Parameter(
                    name = "role",
                    description = "Роль пользователя",
                    required = true,
                    schema = @Schema(implementation = Roles.class),
                    in = ParameterIn.QUERY)
            Roles role
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @PostMapping(
            value = "/orders",
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    @Operation(
            summary = "Получение списка заявок по фильтру",
            description = """
                    Возвращает список заявок, отфильтрованных по заданным параметрам.
                    Для администраторов возвращаются все заявки, для обычных пользователей - только их собственные.
                    Поддерживает пагинацию, сортировку и полнотекстовый поиск.
                    """,
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Успешный запрос. Возвращает список DTO заявок",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = PaginatedResult.class)
                            )),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Неверные параметры запроса (невалидный фильтр)"
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Внутренняя ошибка сервера"
                    )
            }
    )
    default ResponseEntity<PaginatedResult> getPurchaseOrders(
            @Parameter(
                    name = "filter",
                    description = "Фильтр для получения заявок",
                    required = true,
                    schema = @Schema(implementation = RequestsFilter.class)
            )
            @RequestBody @Valid RequestsFilter filter,
            @Parameter(
                    name = "userId",
                    description = "ID пользователя, выполняющего запрос",
                    required = true,
                    example = "12345"
            ) @RequestParam(name = "userId") Long userId,
            @Parameter(
                    name = "role",
                    description = "Роль пользователя",
                    required = true,
                    schema = @Schema(implementation = Roles.class),
                    in = ParameterIn.QUERY)
            Roles role,
            @Parameter(
                    name = "searchText",
                    description = "Текст для полнотекстового поиска по заявкам",
                    example = "срочный заказ"
            ) @RequestParam(name = "searchText", required = false) String searchText
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @GetMapping(
            value = "/sources",
            produces = {"application/json"}
    )
    @Operation(
            summary = "Получение списка источников заявок",
            description = "Получение списка источников заявок",
            responses = {
                    @ApiResponse(responseCode = "200", description = "OK",
                            content = {@Content(mediaType = "application/json", schema = @Schema(implementation = OrderSources.class))}),
                    @ApiResponse(responseCode = "400", description = "Bad Request")
            }
    )
    default ResponseEntity<OrderSources> getSources() {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @PostMapping(
            value = "/{orderId}/returnToWork",
            produces = {"application/json"}
    )
    @Operation(
            summary = "Возврат заявки в работу",
            description = "Возврат заявки в работу",
            responses = {
                    @ApiResponse(responseCode = "200", description = "OK"),
                    @ApiResponse(responseCode = "400", description = "Bad Request")
            }
    )
    default ResponseEntity<PurchaseOrderFullDTO> returnToWork(
            @Parameter(
                    name = "userId",
                    description = "ID пользователя, выполняющего запрос",
                    required = true,
                    example = "12345"
            ) @RequestParam(name = "userId") Long userId,
            @Parameter(
                    name = "orderId",
                    description = "ID заявки",
                    required = true) @PathVariable Long orderId,
            @Parameter(
                    name = "comment",
                    description = "Описание причины возврата",
                    required = true) @RequestParam String comment) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @PostMapping(
            value = "/process-order",
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    @Operation(
            summary = "Обработка информации о заказе",
            description = """
                    Обрабатывает информацию о заказе:
                    - Ищет все заявки в статусе AWAITING_CLIENT_ANSWER для указанного clientId
                    - В этих заявках ищет в привязанных offer и proposedOffer указанный productId
                    - Если найдено, добавляет заказ в orderId и в список orders в purchaseOrder
                    - Возвращает пары [bitrixId, orderId] для предложений с isSentToCustomer = true
                    """,
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Успешный запрос",
                            content = @Content(
                                    mediaType = "application/json",
                                    array = @ArraySchema(schema = @Schema(implementation = OfferPairDTO.class))
                            )
                    ),
                    @ApiResponse(
                            responseCode = "204",
                            description = "Соответствие не найдено"
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Неверные параметры запроса"
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Внутренняя ошибка сервера"
                    )
            }
    )
    default ResponseEntity<List<OfferPairDTO>> processOrder(
            @Parameter(
                    name = "orderInfo",
                    description = "Информация о заказе",
                    required = true,
                    schema = @Schema(implementation = OrderInfoDTO.class)
            )
            @RequestBody @Valid OrderInfoDTO orderInfo) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}
