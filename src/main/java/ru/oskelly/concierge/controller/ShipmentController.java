package ru.oskelly.concierge.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.ShipmentCommentDTO;
import ru.oskelly.concierge.controller.dto.ShipmentRequestDTO;
import ru.oskelly.concierge.controller.dto.ShipmentResponseDTO;
import ru.oskelly.concierge.controller.dto.UpdateProposedOffer;
import ru.oskelly.concierge.service.ShipmentService;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class ShipmentController implements ShipmentApiDelegate {

    private final ShipmentService shipmentService;

    @Override
    public ResponseEntity<ShipmentResponseDTO> addShipmentToOrder(Long orderId, Long userId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(shipmentService.addEmptyShipmentToOrder(orderId, userId));
    }

    @Override
    public ResponseEntity<Void> deleteShipment(Long shipmentId, Long userId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        shipmentService.deleteShipment(shipmentId);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<ShipmentResponseDTO> updateShipment(Long shipmentId, Long userId, ShipmentRequestDTO shipmentRequest) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(shipmentService.updateShipment(shipmentId, shipmentRequest));
    }

    @Override
    public ResponseEntity<ShipmentResponseDTO> addCommentToShipment(Long userId, ShipmentCommentDTO comment) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(shipmentService.addCommentToShipment(comment));
    }

    @Override
    public ResponseEntity<List<ShipmentResponseDTO>> addShipmentsToOrder(
        Long orderId,
        Long userId,
        List<ShipmentRequestDTO> shipments
    ) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(shipmentService.addShipmentsToOrder(shipments, orderId));
    }

    @Override
    public ResponseEntity<ShipmentResponseDTO> getShipmentById(Long shipmentId, Long userId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(shipmentService.getShipmentById(shipmentId));
    }

    @Override
    public ResponseEntity<Void> updateProductIdInProposedOffer(List<UpdateProposedOffer> proposedOffers, Long userId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(shipmentService.updateProductIdInProposedOffer(proposedOffers));
    }
}
