package ru.oskelly.concierge.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import ru.oskelly.concierge.controller.dto.ShipmentCommentDTO;
import ru.oskelly.concierge.controller.dto.ShipmentRequestDTO;
import ru.oskelly.concierge.controller.dto.ShipmentResponseDTO;
import ru.oskelly.concierge.controller.dto.UpdateProposedOffer;

import java.util.List;

@Validated
@Tag(name = "shipment-controller", description = "API для работы с товарами в заявках")
@RequestMapping("/api/v1/shipments")
public interface ShipmentApiDelegate {

    @Operation(
            operationId = "addShipmentToOrder",
            summary = "Добавление товара в заявку",
            description = "Добавляет новый пустой товар в указанную заявку",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Товар успешно добавлен",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ShipmentResponseDTO.class)
                            )
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Заявка не найдена"
                    )
            }
    )
    @PostMapping(
            value = "/purchase-order/{orderId}",
            produces = {"application/json"}
    )
    default ResponseEntity<ShipmentResponseDTO> addShipmentToOrder(
            @Parameter(name = "orderId", description = "ID заявки", required = true, in = ParameterIn.PATH)
            @PathVariable Long orderId,
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam Long userId) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "deleteShipment",
            summary = "Удаление товара из заявки",
            description = "Удаляет товар из заявки. Доступно только для заявок в статусе 'В работе у Sales'",
            responses = {
                    @ApiResponse(
                            responseCode = "204",
                            description = "Товар успешно удален"
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Невозможно удалить товар (неверный статус заявки)"
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Товар или заявка не найдены"
                    )
            }
    )
    @DeleteMapping(
            value = "/{shipmentId}",
            produces = {"application/json"}
    )
    default ResponseEntity<Void> deleteShipment(
            @Parameter(name = "shipmentId", description = "ID товара", required = true, in = ParameterIn.PATH)
            @PathVariable Long shipmentId,
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam Long userId) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "updateShipment",
            summary = "Обновление информации о товаре",
            description = "Частично обновляет информацию о товаре (PATCH запрос)",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Информация о товаре успешно обновлена",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ShipmentResponseDTO.class)
                            )
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Товар не найден"
                    )
            }
    )
    @PatchMapping(
            value = "/{shipmentId}",
            produces = {"application/json"},
            consumes = {"application/json"}
    )
    default ResponseEntity<ShipmentResponseDTO> updateShipment(
            @Parameter(name = "shipmentId", description = "ID товара", required = true, in = ParameterIn.PATH)
            @PathVariable Long shipmentId,
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные для обновления товара",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ShipmentRequestDTO.class)
                    ))
            @RequestBody @Valid ShipmentRequestDTO shipmentRequest) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "addCommentToShipment",
            summary = "Добавление комментария к товару",
            description = "Добавляет комментарий к товару в заявке",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Комментарий успешно добавлен",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ShipmentResponseDTO.class)
                            )
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Товар не найден"
                    )
            }
    )
    @PutMapping(
            value = "/comment",
            produces = {"application/json"},
            consumes = {"application/json"}
    )
    default ResponseEntity<ShipmentResponseDTO> addCommentToShipment(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Комментарий к товару",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ShipmentCommentDTO.class)
                    ))
            @RequestBody ShipmentCommentDTO comment) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "addShipmentsToOrder",
            summary = "Добавление нескольких товаров в заявку",
            description = "Добавляет несколько новых товаров в указанную заявку",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Товары успешно добавлены",
                            content = @Content(
                                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                                    array = @ArraySchema(schema = @Schema(implementation = ShipmentResponseDTO.class))
                            )
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Заявка не найдена"
                    )
            }
    )
    @PostMapping(
            value = "/shipments/purchase-order/{orderId}",
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    default ResponseEntity<List<ShipmentResponseDTO>> addShipmentsToOrder(
            @Parameter(name = "orderId", description = "ID заявки", required = true, in = ParameterIn.PATH)
            @PathVariable Long orderId,

            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam Long userId,

            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Запрос с данными для создания товаров в заявке",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = ShipmentRequestDTO.class))
                    )
            )
            @RequestBody
            @Valid
            @Size(max = 5, message = "Количество товаров не должно быть больше 5")
            List<ShipmentRequestDTO> shipments
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "getShipmentById",
            summary = "Получение товара с предложениями",
            description = "Возвращает товар, с предложениями"
    )
    @GetMapping(
            value = "/{shipmentId}",
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    default ResponseEntity<ShipmentResponseDTO> getShipmentById(
            @Parameter(name = "shipmentId", description = "ID товара", required = true, in = ParameterIn.PATH)
            @PathVariable Long shipmentId,
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam Long userId
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "updateProductIdInProposedOffer",
            summary = "Обновление ID товаров в предложении",
            description = "Обновляет ID товаров в предложении",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "ID товаров успешно обновлены"
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Товар или заявка не найдены"
                    )}
    )
    @PatchMapping(
            value = "/updateProductIdInProposedOffer",
            produces = {"application/json"}
    )
    default ResponseEntity<Void> updateProductIdInProposedOffer(
            @RequestBody @Valid List<UpdateProposedOffer> updateProposedOffer,
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam Long userId) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}