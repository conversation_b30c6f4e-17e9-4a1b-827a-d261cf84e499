package ru.oskelly.concierge.controller.miniapps;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.controller.dto.miniapps.PaginatedOffersResult;
import ru.oskelly.concierge.controller.dto.miniapps.filter.ShopperFilterItemsRequest;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.service.OfferService;

/**
 * Контроллер для работы с предложениями в мини-приложениях
 */
@RestController
@RequiredArgsConstructor
public class OfferShopperController implements OfferShopperApiDelegate {
    
    private final OfferService offerService;

    @Override
    public ResponseEntity<OfferDetailsDTO> getOfferDetails(Long userId, Long offerId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        ThreadLocalContext.put(ContextConstants.ROLE, Roles.PERSONAL_SHOPPER);
        OfferDetailsDTO offerDetails = offerService.getOfferDetails(offerId);
        return ResponseEntity.ok(offerDetails);
    }

    @Override
    public ResponseEntity<PaginatedOffersResult> getOffers(Long userId, ShopperFilterItemsRequest request) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        ThreadLocalContext.put(ContextConstants.ROLE, Roles.PERSONAL_SHOPPER);
        PaginatedOffersResult result = offerService.getShopperOffers(userId, request);
        return ResponseEntity.ok(result);
    }
}
