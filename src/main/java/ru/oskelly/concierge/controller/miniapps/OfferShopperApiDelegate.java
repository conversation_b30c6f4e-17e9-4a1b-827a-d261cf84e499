package ru.oskelly.concierge.controller.miniapps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.controller.dto.miniapps.PaginatedOffersResult;
import ru.oskelly.concierge.controller.dto.miniapps.filter.ShopperFilterItemsRequest;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;

@Validated
@Tag(name = "shopper-offers-controller", description = "API для работы с предложениями в мини-приложениях")
@RequestMapping("/api/v1/shopper/offers")
public interface OfferShopperApiDelegate {

    @Operation(
            summary = "Получение деталей оффера",
            description = "Возвращает детальную информацию об оффере для мини-приложений",
            operationId = "getOfferDetails",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Успешный запрос",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = OfferDetailsDTO.class)
                            )),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Оффер не найден"
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Неверные параметры запроса"
                    )
            }
    )
    @GetMapping(
            value = "/{offerId}",
            produces = {"application/json"}
    )
    default ResponseEntity<OfferDetailsDTO> getOfferDetails(
            @Parameter(
                    name = "userId",
                    description = "ID пользователя",
                    required = true,
                    in = ParameterIn.QUERY,
                    example = "12345")
            @RequestParam(name = "userId") Long userId,

            @Parameter(
                    name = "offerId",
                    description = "ID оффера для получения деталей",
                    required = true,
                    in = ParameterIn.PATH,
                    example = "67890")
            @PathVariable(name = "offerId") Long offerId) {

        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            summary = "Получение списка офферов шоппера",
            description = "Возвращает список офферов шоппера с фильтрацией по статусам и пагинацией",
            operationId = "getOffers",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Успешный запрос",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = PaginatedOffersResult.class)
                            )),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Неверные параметры запроса"
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Пользователь не найден"
                    )
            }
    )
    @PostMapping(
            produces = {"application/json"}
    )
    default ResponseEntity<PaginatedOffersResult> getOffers(
            @Parameter(
                    name = "userId",
                    description = "ID пользователя (шоппера)",
                    required = true,
                    in = ParameterIn.QUERY,
                    example = "12345")
            @RequestParam(name = "userId") Long userId,
            @RequestBody ShopperFilterItemsRequest request
    ) {

        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}
