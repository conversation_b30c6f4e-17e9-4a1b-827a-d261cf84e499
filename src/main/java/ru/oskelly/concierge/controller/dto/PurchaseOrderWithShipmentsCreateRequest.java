package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import ru.oskelly.concierge.controller.dto.managerdto.CustomerInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.controller.validation.PurchaseOrderStatusSubset;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.util.List;

import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.AWAITING_SOURCER;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.DRAFT;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.NEW;

/**
 * Запрос на создание заявки со списком товаров
 */
@Schema(description = "DTO для создания новой заявки на покупку со списком товаров")
public record PurchaseOrderWithShipmentsCreateRequest(
        @Schema(description = "Информация о покупателе", requiredMode = Schema.RequiredMode.REQUIRED)
        CustomerInfoDTO customerInfo,

        @Schema(description = "Информация о менеджере подбора предложения", requiredMode = Schema.RequiredMode.REQUIRED)
        SourcerInfoDTO sourcerInfo,

        @Schema(description = "Информация о менеджере по продажам", requiredMode = Schema.RequiredMode.REQUIRED)
        SalesInfoDTO salesInfo,

        @Schema(
            description = "Источник создания заявки",
            requiredMode = Schema.RequiredMode.REQUIRED,
            defaultValue = "SALES_APP"
        )
        @NotNull(message = "Источник заявки обязателен для заполнения")
        PurchaseOrderSourceEnum source,

        @Schema(description = "Описание заявки", example = "Необходимо закупить офисную технику")
        String description,

        @Schema(description = "Ссылки на изображения")
        List<String> imagesUrl,

        @Schema(description = "Начальный статус заявки", example = "AWAITING_SOURCER", defaultValue = "AWAITING_SOURCER")
        @PurchaseOrderStatusSubset(anyOf = {DRAFT, NEW, AWAITING_SOURCER})
        PurchaseOrderStatusEnum initialStatus,

        @Schema(description = "Товары в заявке")
        @Valid
        List<ShipmentRequestDTO> shipments
) {
}
