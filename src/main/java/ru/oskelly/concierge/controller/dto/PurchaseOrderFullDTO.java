package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.managerdto.CustomerInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * DTO для {@link PurchaseOrder} - полная информация о заказе
 */
@Schema(description = "Полные данные о заказе на покупку, включая всю связанную информацию")
public record PurchaseOrderFullDTO(
        @Schema(description = "Уникальный идентификатор заказа", example = "12345")
        Long id,

        @Schema(description = "ID клиента, оформившего заказ", example = "67890")
        CustomerInfoDTO customer,

        @Schema(description = "Источник, откуда поступил заказ",
                implementation = PurchaseOrderSourceEnum.class)
        PurchaseOrderSourceEnum source,

        @Schema(description = "Описание заказа", example = "Канцтовары для офиса на 3 квартал")
        String description,

        @Schema(description = "Дата и время создания заказа", example = "2023-07-15T10:30:45Z")
        ZonedDateTime creationDate,

        @Schema(description = "Дата и время последнего изменения заказа", example = "2023-07-16T14:25:30Z")
        ZonedDateTime changeDate,

        @Schema(description = "Текущий статус заказа",
                implementation = DescriptionStructureEnum.class)
        DescriptionStructureEnum status,

        @Schema(
                description = "Информация по менеджеру подбора предложения заказа",
                implementation = SourcerInfoDTO.class
        )
        SourcerInfoDTO sourcerInfo,

        @Schema(
                description = "Информация по менеджеру по продажам",
                implementation = SalesInfoDTO.class
        )
        SalesInfoDTO salesInfo,

        @Schema(description = "Список изображений, связанных с заказом")
        List<ImageDTO> images,

        @Schema(description = "Список ID заказов, включенных в этот заказ на покупку")
        List<Long> orders,

        @Schema(description = "Список комментариев к этому заказу")
        List<CommentFullDTO> comments,

        @Schema(description = "Причина отказа в заказе, если применимо", example = "Нет в наличии")
        String rejectionReason,

        @Schema(description = "Подробное описание причины отказа, если применимо",
                example = "Запрашиваемые товары в настоящее время отсутствуют на складе и будут доступны только в следующем месяце")
        String rejectionDescription,

        @Schema(description = "Список товаров, включенных в этот заказ на покупку", type = "array")
        @ArraySchema(schema = @Schema(implementation = ShipmentResponseDTO.class))
        List<ShipmentResponseDTO> shipments,

        @Schema(description = "Ссылка на товар", example = "www.jacquemus.com")
        String link,

        @Schema(description = "Идентификатор сделки bitrix", example = "1")
        Long bitrixDealId
) implements Serializable {
}