package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import ru.oskelly.concierge.data.model.enums.Currency;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Schema(description = "Данные о предложении продавца")
public record ProposedOfferDTO(
        @Schema(description = "Уникальный идентификатор предложения", example = "123")
        Long id,

        @Schema(description = "Товар созданный по предложению шоппера", example = "12345")
        ProductPlatformDTO proposedProduct,

        @Schema(description = "Цена предложения в рублях", example = "15000.00")
        BigDecimal rublePrice,

        @Schema(description = "Дата доставки по предложению",
                example = "2023-12-31T15:00:00+03:00",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        ZonedDateTime deliveryDate,

        @Schema(description = "Срок действия предложения",
                example = "2023-12-15T23:59:59+03:00",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        ZonedDateTime validUntil,

        @Schema(description = "Дата создания предложения", example = "2023-11-30T12:00:00+03:00")
        ZonedDateTime creationDate,

        @Schema(description = "Валюта закупки",
                implementation = Currency.class,
                example = "EUR",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Currency currency,

        @Schema(description = "Цена в валюте закупки",
                example = "150.00",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        BigDecimal currencyPrice,

        @Schema(description = "Курс валюты к рублю",
                example = "90.45",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        BigDecimal currencyRate,

        @Schema(description = "Флаг наличия чека",
                example = "true",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Boolean hasReceipt,

        @Schema(description = "Флаг полного комплекта товара",
                example = "true",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Boolean isCompleteSet,

        @Schema(description = "Флаг произвольной комиссии",
                example = "false",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Boolean hasCustomCommission,

        @Schema(description = "Размер комиссии в процентах",
                example = "15.00",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        BigDecimal commission,

        @Schema(description = "Комментарий к предложению",
                example = "Товар в отличном состоянии, найден в бутике в Милане",
                requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        String comment,

        @Schema(description = "Идентификатор заказа", example = "200")
        Long orderId,

        @Schema(description = "Информация о состоянии товара",
                example = "1",
                requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        @Valid
        ProductConditionDTO productCondition,

        @Schema(description = "Идентификатор оффера, к которому относится предложение", example = "456")
        Long offerId
) {}