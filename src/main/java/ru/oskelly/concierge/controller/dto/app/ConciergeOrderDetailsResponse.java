package ru.oskelly.concierge.controller.dto.app;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;
import ru.oskelly.concierge.data.model.enums.ShipmentStatus;

import java.time.ZonedDateTime;
import java.util.List;

@Schema(description = "Детальный ответ по заказу консьержа, включает статус, детали заказа и отгрузки")
public record ConciergeOrderDetailsResponse(
    @Schema(description = "Информация о статусе заказа")
    StatusInfo statusInfo,
    @Schema(description = "Детали заказа")
    OrderDetails orderDetails,
    @Schema(description = "Список отгрузок по заказу")
    @ArraySchema(schema = @Schema(implementation = Shipment.class))
    List<Shipment> shipments
) {
    @Schema(description = "Информация о статусе PurchaseOrder")
    public record StatusInfo(
        @Schema(description = "Код статуса", example = "IN_PROGRESS")
        String status,
        @Schema(description = "Заголовок статуса", example = "В процессе")
        String title,
        @Schema(description = "Описание статуса", example = "Заказ находится в обработке")
        String description
    ) {}

    @Schema(description = "Детали заказа из PurchaseOrder")
    public record OrderDetails(
        @Schema(description = "ID заказа", example = "123456")
        Long id,
        @Schema(description = "Дата создания заказа", example = "2024-03-15T10:30:00Z")
        ZonedDateTime creationDate,
        @Schema(description = "Описание заказа")
        String description,
        @Schema(description = "Ссылки на прикрепленные фотографии")
        @ArraySchema(schema = @Schema(implementation = ImageDTO.class))
        List<ImageDTO> attachedPhotos
    ) {}

    @Schema(description = "Информация об отгрузке")
    public record Shipment(
            @Schema(description = "ID", example = "789")
            Long id,
            @Schema(description = "Бренд товара", example = "Nike")
            String brand,
            @Schema(description = "Размер товара", example = "M")
            ShimpentSizeDTO size,
            @Schema(description = "Категория товара")
            String category,
            @Schema(description = "Модель товара", example = "AirMax")
            String model,
            @Schema(description = "Цвет товара", example = "Черный")
            String color,
            @Schema(description = "Материал товара", example = "Хлопок")
            String material,
            @Schema(description = "Ссылка на товар", example = "https://example.com/product/airmax   ")
            String link,
            @Schema(description = "Описание товара")
            String description,
            @ArraySchema(schema = @Schema(implementation = ImageDTO.class))
            List<ImageDTO> attachedPhotos,
            @Schema(description = "Дата создания отгрузки", example = "2024-03-15T10:30:00Z")
            ZonedDateTime creationDate,
            @Schema(description = "Статус", implementation = ShipmentStatus.class)
            ShipmentStatus shipmentStatus,
            @ArraySchema(schema = @Schema(implementation = OfferApp.class))
            List<OfferApp> productsPlatform,
            @Schema(description = "Информация о заказе, к которому относится отгрузка")
            @ArraySchema(schema = @Schema(implementation = Order.class))
            List<Order> ordersMono
    ) {}

    @Schema(description = "Информация о предложении по отгрузке")
    public record OfferApp(
        @Schema(description = "ID предложения", example = "101")
        Long id,
        @Schema(description = "Тег предложения", example = "Лучшая цена")
        String titleTag,
        @Schema(description = "Дата истечения предложения", example = "2024-03-20T10:30:00Z")
        ZonedDateTime expirationDate,
        @Schema(description = "Цена предложения")
        Price price,
        @Schema(description = "Дата доставки по предложению", example = "2024-03-25T10:30:00Z")
        ZonedDateTime deliveredDate,
        @Schema(description = "Флаг, что предложение истекло", example = "false")
        boolean isExpired,
        @Schema(description = "Флаг, что предложение продано", example = "false")
        boolean isSold

    ) {}

    @Schema(description = "Информация о заказе внутри")
    public record Order(
        @Schema(description = "ID заказа", example = "202")
        Long id,
        @Schema(description = "Информация о пользователе")
        UserInfo userInfo,
        @Schema(description = "Цена заказа")
        Price price,
        @Schema(description = "Дата доставки заказа", example = "2024-03-25T10:30:00Z")
        ZonedDateTime deliveredDate
    ) {}

    @Schema(description = "Информация о пользователе")
    public record UserInfo(
            @Schema(description = "ID пользователя", example = "303")
            Long id,
            @Schema(description = "Ссылка на аватар пользователя", example = "https://example.com/avatar.jpg   ")
            String avatarUrl,
            @Schema(description = "Никнейм пользователя", example = "john_doe")
            String nickname,
            @Schema(description = "Роль пользователя")
            String type
    ) {}

    @Schema(description = "Цена")
    public record Price(
        @Schema(description = "Сумма", example = "100.0")
        Double amount,
        @Schema(description = "Валюта", example = "USD")
        String currency
    ) {}
}
