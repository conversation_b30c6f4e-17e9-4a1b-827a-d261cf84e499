package ru.oskelly.concierge.config;

import io.swagger.v3.core.converter.ModelConverters;
import io.swagger.v3.oas.models.media.Schema;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.app.ConciergeOrderDetailsResponse;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.controller.dto.miniapps.PaginatedOffersResult;
import ru.oskelly.concierge.controller.dto.miniapps.ShortOfferDTO;
import ru.oskelly.concierge.controller.dto.app.ConciergeOrderDetailsResponse;
import ru.oskelly.concierge.controller.mercaux.dto.MercauxPurchaseOrderFullDTO;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Configuration
public class OpenApiConfig {
    private static final String ENUMS_BASE_PACKAGE = "ru.oskelly.concierge.";

    @Bean
    @SuppressWarnings("rawtypes")
    public OpenApiCustomizer addUnusedDtosToSchema() {
        return openApi -> {
            // Список классов для сканирования
            Class<?>[] dtos = {
                    DescriptionStructureEnum.class,
                    MercauxPurchaseOrderFullDTO.class,
                    OfferDetailsDTO.class,
                    ShortOfferDTO.class,
                    PaginatedOffersResult.class,
                    ConciergeOrderDetailsResponse.class
            };
            Map<String, Schema> schemas = new LinkedHashMap<>();

            // Сканируем все классы
            for (Class<?> dto : dtos) {
                schemas.putAll(ModelConverters.getInstance().readAll(dto));
            }

            // Проверяем и добавляем схемы
            if (openApi.getComponents().getSchemas() == null) {
                openApi.getComponents().setSchemas(new HashMap<>());
            }

            openApi.getComponents().getSchemas().putAll(schemas);
        };
    }


    @Bean
    public OpenApiCustomizer enumCustomizer() {
        return openApi -> {
            // Получаем все схемы из компонентов
            Map<String, Schema> schemas = openApi.getComponents().getSchemas();
            if (schemas == null) {
                return;
            }

            // Итерируемся по всем схемам
            schemas.forEach((schemaName, schema) -> {
                // Проверяем, является ли схема enum'ом (есть поле enum и это строка)
                if (schema.getEnum() != null && !schema.getEnum().isEmpty() && "string".equals(schema.getType())) {
                    try {
                        // Пытаемся найти Java-класс для этой схемы
                        Class<?> enumClass = Class.forName(ENUMS_BASE_PACKAGE + schemaName);

                        // Убеждаемся, что это действительно enum
                        if (enumClass.isEnum()) {
                            addEnumVarsExtension(schema, enumClass);
                        }
                    } catch (ClassNotFoundException | SecurityException e) {
                        // Игнорируем, если класс не найден или это не наш enum
                        // Можно добавить логирование для отладки
                        // log.debug("Could not find class for schema {}, skipping x-enum-vars generation.", schemaName);
                    }
                }
            });
        };
    }

    private void addEnumVarsExtension(Schema<?> schema, Class<?> enumClass) {
        List<Map<String, String>> enumVars = new ArrayList<>();
        try {
            // Ищем метод, который возвращает строковое значение (в вашем случае getValue())
            Method valueMethod = enumClass.getMethod("getValue");

            for (Object enumConstant : enumClass.getEnumConstants()) {
                Map<String, String> enumVar = new LinkedHashMap<>();
                enumVar.put("name", ((Enum<?>) enumConstant).name());

                // Вызываем метод getValue() для получения строкового значения
                String value = (String) valueMethod.invoke(enumConstant);
                enumVar.put("value", value);

                enumVars.add(enumVar);
            }

            // Добавляем кастомное расширение в схему
            schema.addExtension("x-enum-vars", enumVars);

        } catch (Exception e) {
            // Если что-то пошло не так (например, нет метода getValue), просто не добавляем расширение
            // Можно добавить логирование
            // log.error("Failed to generate x-enum-vars for enum {}", enumClass.getSimpleName(), e);
        }
    }
}
